﻿using System.Xml.Linq;
using CommandLine;
using PimDataModel;

// Command line options
[Verb("generate", HelpText = "Generate data models from XML schema files")]
public class GenerateOptions
{
    [Option(
        's',
        "schema",
        Required = false,
        Default = "../../Atvero/AtveroSchema/schema/master",
        HelpText = "Path to the schema directory containing XML files"
    )]
    public string SchemaPath { get; set; } = "../../Atvero/AtveroSchema/schema/master";

    [Option('c', "class", Required = false, HelpText = "Generate only a specific class (optional)")]
    public string? ClassName { get; set; }

    [Option('v', "verbose", Required = false, Default = false, HelpText = "Enable verbose output")]
    public bool Verbose { get; set; }
}

// Main program entry point
public class Program
{
    public static int Main(string[] args)
    {
        return Parser
            .Default.ParseArguments<GenerateOptions>(args)
            .MapResult((GenerateOptions opts) => RunGenerate(opts), errs => 1);
    }

    private static int RunGenerate(GenerateOptions options)
    {
        try
        {
            var generator = new ModelGenerator(options.SchemaPath, options.Verbose);

            generator.GenerateAllModels();

            Console.WriteLine("Model generation completed successfully!");
            return 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            if (options.Verbose)
            {
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            return 1;
        }
    }
}
