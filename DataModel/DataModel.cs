using System.Xml.Linq;
using CommandLine;
using PimDataModel;

// Command line options
[Verb("generate", HelpText = "Generate data models from XML schema files")]
public class GenerateOptions
{
    [Option(
        's',
        "schema",
        Required = false,
        HelpText = "Path to the schema directory containing XML files"
    )]
    public string SchemaPath { get; set; }

    [Option('c', "class", Required = false, HelpText = "Generate only a specific class (optional)")]
    public string? ClassName { get; set; }

    [Option('v', "verbose", Required = false, Default = false, HelpText = "Enable verbose output")]
    public bool Verbose { get; set; }
}

// Main program entry point
public class Program
{
    public static int Main(string[] args)
    {
        return Parser
            .Default.ParseArguments<GenerateOptions>(args)
            .MapResult((GenerateOptions opts) => RunGenerate(opts), errs => 1);
    }

    private static int RunGenerate(GenerateOptions options)
    {
        try
        {
            var generator = new ModelGenerator(options.Verbose);

            if (string.IsNullOrEmpty(options.SchemaPath))
            {
                string tempDirectory = Path.Combine(Path.GetTempPath(), "66");

                if (System.IO.File.Exists(tempDirectory)) { }
                else
                {
                    Directory.CreateDirectory(tempDirectory);
                }
                options.SchemaPath = tempDirectory;

                HttpClient sourceClient = new HttpClient();
                foreach (string filename in generator.classTables.Keys.Select(x => x + ".xml"))
                {
                    bool success = DownloadFile(
                            sourceClient,
                            "https://schema.atvero.com",
                            tempDirectory,
                            filename
                        )
                        .GetAwaiter()
                        .GetResult();

                    if (!success)
                    {
                        Console.WriteLine("Failed to download source file for " + filename);
                        return -1;
                    }
                }
            }

            generator.GenerateAllModels(options.SchemaPath);

            Console.WriteLine("Model generation completed successfully!");
            return 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            if (options.Verbose)
            {
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            return 1;
        }
    }

    private static async Task<bool> DownloadFile(
        HttpClient sourceClient,
        string sourceUrl,
        string destinationFolder,
        string filename
    )
    {
        Uri uri = new Uri(sourceUrl + "/66/" + filename);
        try
        {
            string desinationFile = Path.Combine(destinationFolder, filename);
            if (!System.IO.File.Exists(desinationFile))
            {
                Console.WriteLine("Downloading source file for " + filename);
                using (HttpResponseMessage response = await sourceClient.GetAsync(uri))
                {
                    if (response.IsSuccessStatusCode)
                    {
                        using (var fs = new FileStream(desinationFile, FileMode.CreateNew))
                        {
                            await response.Content.CopyToAsync(fs);
                            return true;
                        }
                    }
                    else
                    {
                        Console.WriteLine("Unable to download source file for  " + filename);
                        return false;
                    }
                }
            }
            else
            {
                return true;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("Failed to copy file " + filename + " from " + uri.ToString());
            Console.WriteLine(ex.Message);
            return false;
        }
    }
}
