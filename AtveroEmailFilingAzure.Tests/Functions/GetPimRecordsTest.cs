using System.Net;
using AtveroEmailFiling.Functions;
using AtveroEmailFiling.Models;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFilingAzure.Tests.Helpers;
using CMapPim.Services;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Moq;

namespace AtveroEmailFilingAzure.Tests.Functions
{
    [TestClass]
    public class GetPimRecordsTest
    {
        private Mock<ILogger<GetPimRecords>> _mockLogger;
        private Mock<ITokenValidationService> _mockTokenValidationService;
        private Mock<IGraphApiClient> _mockGraphApiClient;
        private Mock<IGraphClientService> _mockGraphClientService;
        private Mock<ICMapPimService> _mockCMapPimService;
        private GetPimRecords _function;

        [TestInitialize]
        public void Setup()
        {
            _mockLogger = new Mock<ILogger<GetPimRecords>>();
            _mockTokenValidationService = new Mock<ITokenValidationService>();
            _mockGraphApiClient = new Mock<IGraphApiClient>();
            _mockGraphClientService = new Mock<IGraphClientService>();
            _mockCMapPimService = new Mock<ICMapPimService>();

            _function = new GetPimRecords(
                _mockLogger.Object,
                _mockTokenValidationService.Object,
                _mockGraphApiClient.Object,
                _mockGraphClientService.Object,
                _mockCMapPimService.Object
            );
        }

        [TestMethod]
        public async Task Run_WithValidRecordName_ReturnsSuccess()
        {
            // Arrange
            var recordName = "test-project";
            var token = "valid-token";
            var upn = "<EMAIL>";
            var tenant = "contoso";

            var tokenDetails = new TokenDetails
            {
                Upn = upn,
                ClientId = "client-id",
                ClientSecret = "client-secret",
            };

            var mockGraphClient = new Mock<GraphServiceClient>();

            var req = new FakeHttpRequestData(
                new Mock<FunctionContext>().Object,
                new Uri($"https://example.com?recordName={recordName}")
            );
            req.Headers.Add("Authorization", $"Bearer {token}");

            _mockTokenValidationService
                .Setup(x => x.ValidateCommonAuthorizationHeaderAsync(It.IsAny<HttpRequestData>()))
                .ReturnsAsync(token);

            _mockTokenValidationService.Setup(x => x.GetUserDetails(token)).Returns(tokenDetails);

            _mockGraphApiClient
                .Setup(x => x.GetSpTenantNameAsync(It.IsAny<GraphServiceClient>()))
                .ReturnsAsync(tenant);

            _mockGraphClientService
                .Setup(x =>
                    x.GetUserGraphClient(token, tokenDetails.ClientId, tokenDetails.ClientSecret)
                )
                .Returns(mockGraphClient.Object);

            // Act
            var result = await _function.Run(req, CancellationToken.None);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(HttpStatusCode.OK, result.StatusCode);
        }

        [TestMethod]
        public async Task Run_WithMissingBothParameters_ReturnsBadRequest()
        {
            // Arrange
            var req = new FakeHttpRequestData(
                new Mock<FunctionContext>().Object,
                new Uri("https://example.com")
            );

            // Act
            var result = await _function.Run(req, CancellationToken.None);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(HttpStatusCode.BadRequest, result.StatusCode);
        }

        [TestMethod]
        public async Task Run_WithSiteUrl_ReturnsSuccess()
        {
            // Arrange
            var siteUrl = "https://contoso.sharepoint.com/sites/test-project";
            var token = "valid-token";
            var upn = "<EMAIL>";
            var tenant = "contoso";

            var tokenDetails = new TokenDetails
            {
                Upn = upn,
                ClientId = "client-id",
                ClientSecret = "client-secret",
            };

            var mockGraphClient = new Mock<GraphServiceClient>();

            var req = new FakeHttpRequestData(
                new Mock<FunctionContext>().Object,
                new Uri($"https://example.com?siteUrl={Uri.EscapeDataString(siteUrl)}")
            );
            req.Headers.Add("Authorization", $"Bearer {token}");

            _mockTokenValidationService
                .Setup(x => x.ValidateCommonAuthorizationHeaderAsync(It.IsAny<HttpRequestData>()))
                .ReturnsAsync(token);

            _mockTokenValidationService.Setup(x => x.GetUserDetails(token)).Returns(tokenDetails);

            _mockGraphApiClient
                .Setup(x => x.GetSpTenantNameAsync(It.IsAny<GraphServiceClient>()))
                .ReturnsAsync(tenant);

            _mockGraphClientService
                .Setup(x =>
                    x.GetUserGraphClient(token, tokenDetails.ClientId, tokenDetails.ClientSecret)
                )
                .Returns(mockGraphClient.Object);

            // Act
            var result = await _function.Run(req, CancellationToken.None);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(HttpStatusCode.OK, result.StatusCode);
        }

        [TestMethod]
        public async Task Run_WithInvalidToken_ReturnsBadRequest()
        {
            // Arrange
            var recordName = "test-project";
            var req = new FakeHttpRequestData(
                new Mock<FunctionContext>().Object,
                new Uri($"https://example.com?recordName={recordName}")
            );

            _mockTokenValidationService
                .Setup(x => x.ValidateCommonAuthorizationHeaderAsync(It.IsAny<HttpRequestData>()))
                .ReturnsAsync((string?)null);

            // Act
            var result = await _function.Run(req, CancellationToken.None);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(HttpStatusCode.BadRequest, result.StatusCode);
        }

        [TestMethod]
        public async Task Run_WithGraphClientFailure_ReturnsInternalServerError()
        {
            // Arrange
            var recordName = "test-project";
            var token = "valid-token";
            var upn = "<EMAIL>";
            var tenant = "contoso";

            var tokenDetails = new TokenDetails
            {
                Upn = upn,
                ClientId = "client-id",
                ClientSecret = "client-secret",
            };

            var req = new FakeHttpRequestData(
                new Mock<FunctionContext>().Object,
                new Uri($"https://example.com?recordName={recordName}")
            );
            req.Headers.Add("Authorization", $"Bearer {token}");

            _mockTokenValidationService
                .Setup(x => x.ValidateCommonAuthorizationHeaderAsync(It.IsAny<HttpRequestData>()))
                .ReturnsAsync(token);

            _mockTokenValidationService.Setup(x => x.GetUserDetails(token)).Returns(tokenDetails);

            _mockGraphApiClient
                .Setup(x => x.GetSpTenantNameAsync(It.IsAny<GraphServiceClient>()))
                .ReturnsAsync(tenant);

            _mockGraphClientService
                .Setup(x =>
                    x.GetUserGraphClient(token, tokenDetails.ClientId, tokenDetails.ClientSecret)
                )
                .Returns((GraphServiceClient?)null);

            // Act
            var result = await _function.Run(req, CancellationToken.None);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(HttpStatusCode.InternalServerError, result.StatusCode);
        }
    }
}
