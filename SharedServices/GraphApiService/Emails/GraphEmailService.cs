﻿using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using FluentResults;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;

namespace AtveroEmailFiling.Services.GraphApiService.Emails
{
    public class GraphEmailService : IGraphEmailService
    {
        private readonly ILogger<GraphEmailService> _logger;
        private readonly IAzureTableService _tableServiceClient;

        public GraphEmailService(
            ILogger<GraphEmailService> logger,
            IAzureTableService tableServiceClient
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tableServiceClient =
                tableServiceClient ?? throw new ArgumentNullException(nameof(tableServiceClient));
        }

        public async Task<Result<Dictionary<string, string>>> MapFromRestIdToPersistentIDs(
            List<string> originalIds,
            GraphServiceClient graphClient,
            string activeUser
        )
        {
            return await MapToPersistentIDs(
                originalIds,
                graphClient,
                activeUser,
                ExchangeIdFormat.RestId
            );
        }

        public async Task<Result<Dictionary<string, string>>> MapFromEntryIdToPersistentIDs(
            List<string> originalIds,
            GraphServiceClient graphClient,
            string activeUser
        )
        {
            return await MapToPersistentIDs(
                originalIds,
                graphClient,
                activeUser,
                ExchangeIdFormat.EntryId
            );
        }

        private async Task<Result<Dictionary<string, string>>> MapToPersistentIDs(
            List<string> originalIds,
            GraphServiceClient graphClient,
            string activeUser,
            ExchangeIdFormat sourceIdType
        )
        {
            // _logger.LogInformation("MapToPersistentIDs");

            //
            //  _logger.LogInformation("Converting  mailbox message id for " + activeUser);
            var requestBody =
                new Microsoft.Graph.Users.Item.TranslateExchangeIds.TranslateExchangeIdsPostRequestBody
                {
                    InputIds = originalIds,
                    SourceIdType = sourceIdType,
                    TargetIdType = ExchangeIdFormat.RestImmutableEntryId,
                };
            // _logger.LogInformation(
            //     "Request body: " + System.Text.Json.JsonSerializer.Serialize(requestBody)
            // );
            var result = await graphClient
                .Users[activeUser]
                .TranslateExchangeIds.PostAsTranslateExchangeIdsPostResponseAsync(requestBody);

            if (result != null && result.Value != null)
            {
                //_logger.LogInformation("Mapped IDs");

                Dictionary<string, string> mapped = new Dictionary<string, string>();

                foreach (var mail in result.Value)
                {
                    if (mail.SourceId != null && mail.TargetId != null)
                    {
                        //_logger.LogInformation(mail.SourceId + " to  " + mail.TargetId);

                        mapped.Add(mail.SourceId, mail.TargetId);
                    }
                }

                return Result.Ok(mapped);
            }
            else
            {
                _logger.LogInformation("Failed to map IDs");

                return Result.Fail("Unable to map IDs");
            }
        }

        public async Task<Result<Dictionary<string, string>>> MapFromPersistentIDs(
            List<string> originalIds,
            GraphServiceClient graphClient,
            string activeUser
        )
        {
            _logger.LogInformation("MapFROMPersistentIDs");

            var requestBody =
                new Microsoft.Graph.Users.Item.TranslateExchangeIds.TranslateExchangeIdsPostRequestBody
                {
                    InputIds = originalIds,
                    SourceIdType = ExchangeIdFormat.RestImmutableEntryId,
                    TargetIdType = ExchangeIdFormat.RestId,
                };

            var result = await graphClient
                .Users[activeUser]
                .TranslateExchangeIds.PostAsTranslateExchangeIdsPostResponseAsync(requestBody);
            if (result != null && result.Value != null)
            {
                // _logger.LogInformation("Mapped IDs");

                Dictionary<string, string> mapped = new Dictionary<string, string>();

                foreach (var mail in result.Value)
                {
                    if (mail.SourceId != null && mail.TargetId != null)
                    {
                        mapped.Add(mail.SourceId, mail.TargetId);
                    }
                }

                return Result.Ok(mapped);
            }
            else
            {
                _logger.LogInformation("Failed to map from persistent IDs");

                return Result.Fail("Unable to map from persistent IDs");
            }
        }

        public async Task<Result<List<MailFolder>>> GetFilingFolders(
            GraphServiceClient graphClient,
            string activeUser
        )
        {
            try
            {
                //_logger.LogInformation($"Attempting to retrieve folders");

                Microsoft.Graph.Models.MailFolderCollectionResponse? folderResponse =
                    await graphClient.Users[activeUser].MailFolders.GetAsync();

                if (folderResponse != null)
                {
                    List<MailFolder> folders = new List<MailFolder>();
                    var pageIterator = PageIterator<
                        MailFolder,
                        MailFolderCollectionResponse
                    >.CreatePageIterator(
                        graphClient,
                        folderResponse,
                        folder =>
                        {
                            folders.Add(folder);
                            return true;
                        }
                    );

                    await pageIterator.IterateAsync();

                    // remove deleted items

                    var foldersWithoutDeleted = new List<MailFolder>();

                    foreach (MailFolder folder in folders)
                    {
                        if (folder.DisplayName != "Deleted Items")
                        {
                            foldersWithoutDeleted.Add(folder);
                        }
                    }

                    return Result.Ok(foldersWithoutDeleted);
                }
                else
                {
                    return Result.Fail("Unable to get a folders delta");
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("AADSTS500133"))
                {
                    _logger.LogInformation(ex, "Failed as token was expired");
                }
                else
                {
                    _logger.LogInformation($"Failed to retrieve folder " + ex.Message);
                    _logger.LogError(ex, $"Failed to retrieve folders");
                }

                return Result.Fail("Unable to load folders: " + ex.Message);
            }
        }

        // public async Task<Result<List<MailFolder>>> GetFilingFolderByDelta(
        //     GraphServiceClient graphClient,
        //     string activeUser
        // )
        // {
        //     try
        //     {
        //         //_logger.LogInformation($"Attempting to retrieve folders by delta");

        //         Microsoft.Graph.Users.Item.MailFolders.Delta.DeltaGetResponse? deltaResponse =
        //             await graphClient
        //                 .Users[activeUser]
        //                 .MailFolders.Delta.GetAsDeltaGetResponseAsync();

        //         if (deltaResponse != null)
        //         {
        //             List<MailFolder> folders = new List<MailFolder>();
        //             var pageIterator = PageIterator<
        //                 MailFolder,
        //                 Microsoft.Graph.Users.Item.MailFolders.Delta.DeltaGetResponse
        //             >.CreatePageIterator(
        //                 graphClient,
        //                 deltaResponse,
        //                 folder =>
        //                 {
        //                     folders.Add(folder);
        //                     return true;
        //                 }
        //             );

        //             await pageIterator.IterateAsync();

        //             // remove deleted items

        //             var foldersWithoutDeleted = new List<MailFolder>();

        //             foreach (MailFolder folder in folders)
        //             {
        //                 if (folder.DisplayName != "Deleted Items")
        //                 {
        //                     foldersWithoutDeleted.Add(folder);
        //                 }
        //             }

        //             return Result.Ok(foldersWithoutDeleted);
        //         }
        //         else
        //         {
        //             return Result.Fail("Unable to get a folders delta");
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         _logger.LogInformation($"Failed to retrieve folders by delta " + ex.Message);
        //         _logger.LogError(ex, $"Failed to retrieve folders by delta ");

        //         return Result.Fail("Unable to load folders with delta query " + ex.Message);
        //     }
        // }

        public async Task<
            Result<(List<Microsoft.Graph.Models.Message>, FilingFolder?)>
        > GetEmailByDelta(
            GraphServiceClient graphClient,
            string folderId,
            string folderName,
            string userName,
            string customerDomain,
            string rowId, // different for file on send,
            string activeUser
        )
        {
            try
            {
                //  _logger.LogInformation($"Attempting to retrieve emails by delta");

                // see if we have a link for the next fetch

                Result<FilingFolder> filingFolderRes =
                    await _tableServiceClient.GetEntityAsync<FilingFolder>(
                        "FilingFolders",
                        activeUser,
                        rowId,
                        customerDomain
                    );

                Microsoft.Graph.Users.Item.MailFolders.Item.Messages.Delta.DeltaGetResponse? deltaResponse;

                FilingFolder? newFilingFolder = null;

                if (filingFolderRes.IsFailed)
                {
                    _logger.LogInformation("No previous sync item found ");
                    deltaResponse = await graphClient
                        .Users[activeUser]
                        .MailFolders[folderId]
                        .Messages.Delta.GetAsDeltaGetResponseAsync(
                            (requestConfiguration) =>
                            {
                                requestConfiguration.Headers.Add("Prefer", "odata.maxpagesize=500");
                                requestConfiguration.QueryParameters.Select = new string[]
                                {
                                    "id",
                                    "internetMessageId",
                                    "conversationId",
                                    "subject",
                                    "receivedDateTime",
                                    "sentDateTime",
                                    "categories",
                                    "from",
                                    "sender",
                                    "toRecipients",
                                    "ccRecipients",
                                    "bccRecipients",
                                };

                                requestConfiguration.QueryParameters.Filter =
                                    $"receivedDateTime ge {DateTime.UtcNow.Add(TimeSpan.FromDays(-7)):yyyy-MM-dd}";

                                requestConfiguration.QueryParameters.ChangeType = "created";
                            }
                        );
                }
                else
                {
                    // _logger.LogInformation(
                    //     "Using a delta token " + filingFolderRes.Value.DeltaLink
                    // );
                    var deltaRequest =
                        new Microsoft.Graph.Users.Item.MailFolders.Item.Messages.Delta.DeltaRequestBuilder(
                            filingFolderRes.Value.DeltaLink,
                            graphClient.RequestAdapter
                        );
                    deltaResponse = await deltaRequest.GetAsDeltaGetResponseAsync();
                }

                if (deltaResponse != null && deltaResponse.Value != null)
                {
                    List<Microsoft.Graph.Models.Message>? messages = deltaResponse.Value;

                    // _logger.LogInformation($"got {messages.Count} messages in the delta");

                    if (deltaResponse.OdataNextLink != null)
                    {
                        //  _logger.LogInformation($"got OdataNextLink {deltaResponse.OdataNextLink} ");

                        newFilingFolder = new FilingFolder()
                        {
                            PartitionKey = activeUser,
                            RowKey = rowId,
                            FolderName = folderName,
                            DeltaLink = deltaResponse.OdataNextLink,
                        };
                    }

                    if (deltaResponse.OdataDeltaLink != null)
                    {
                        // _logger.LogInformation(
                        //     $"got OdataDeltaLink {deltaResponse.OdataDeltaLink} "
                        // );



                        newFilingFolder = new FilingFolder()
                        {
                            PartitionKey = activeUser,
                            RowKey = rowId,
                            FolderName = folderName,
                            DeltaLink = deltaResponse.OdataDeltaLink,
                        };
                    }

                    return Result.Ok((messages, newFilingFolder));
                }

                return Result.Fail("Delta of messages failed to load");
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Failed accessing message delta " + ex.Message);
                _logger.LogError(ex, $"Exception occurred while retrieving message delta");
                return Result.Fail(
                    $"Exception occurred while retrieving message delta" + ex.Message
                );
            }
        }

        public async Task<Result<List<Microsoft.Graph.Models.Message>>> GetSentItems(
            GraphServiceClient graphClient,
            string folderId,
            string activeUser,
            DateTime? lastSyncTime,
            string[]? addinManifestIds
        )
        {
            try
            {
                _logger.LogInformation($"Attempting to retrieve sent email items");

                var response = await graphClient
                    .Users[activeUser]
                    .MailFolders[folderId]
                    .Messages.GetAsync(
                        (requestConfiguration) =>
                        {
                            requestConfiguration.Headers.Add("Prefer", "odata.maxpagesize=500");
                            requestConfiguration.QueryParameters.Select = new string[]
                            {
                                "id",
                                "internetMessageId",
                                "conversationId",
                                "subject",
                                "receivedDateTime",
                                "sentDateTime",
                                "categories",
                                "from",
                                "sender",
                                "toRecipients",
                                "ccRecipients",
                                "bccRecipients",
                                "SingleValueExtendedProperties",
                            };

                            if (addinManifestIds != null)
                            {
                                _logger.LogInformation("Getting the SingleValueExtendedProperties");

                                // $expand=SingleValueExtendedProperties($filter=id eq 'String {00020329-0000-0000-C000-000000000046} Name cecp-e317c2f5-4cb6-437b-b305-30b84cb2ece0' or
                                // id eq 'String {00020329-0000-0000-C000-000000000046} Name cecp-e317c2f5-4cb6-437b-b305-30b84cb2ece1')

                                List<string> propertynames = addinManifestIds
                                    .Select(addinManifestId =>
                                        @"id eq 'String {00020329-0000-0000-C000-000000000046} Name "
                                        + string.Format(" cecp-{0}", addinManifestId)
                                        + @"'"
                                    )
                                    .ToList();
                                string messageMetadata =
                                    $"SingleValueExtendedProperties($filter={string.Join(" or ", propertynames)})";

                                _logger.LogInformation(messageMetadata);

                                requestConfiguration.QueryParameters.Expand = [messageMetadata];
                            }

                            if (lastSyncTime != null)
                            {
                                _logger.LogInformation("Using last sync time");
                                requestConfiguration.QueryParameters.Filter =
                                    $"receivedDateTime ge {lastSyncTime:yyyy-MM-dd}";
                            }
                            else
                            {
                                _logger.LogInformation(
                                    "No last sync time, falling back to last 2 days"
                                );

                                requestConfiguration.QueryParameters.Filter =
                                    $"receivedDateTime ge {DateTime.UtcNow.Add(TimeSpan.FromDays(-2)):yyyy-MM-dd}";
                            }

                            requestConfiguration.QueryParameters.Top = 100;
                        }
                    );

                if (response != null && response.Value != null)
                {
                    List<Microsoft.Graph.Models.Message>? messages = response.Value;

                    _logger.LogInformation($"got {messages.Count} messages in the delta");

                    return Result.Ok(messages);
                }

                return Result.Fail("Delta of messages failed to load");
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Failed accessing message delta " + ex.Message);
                _logger.LogError(ex, $"Exception occurred while retrieving message delta");
                return Result.Fail(
                    $"Exception occurred while retrieving message delta" + ex.Message
                );
            }
        }
    }
}
