﻿using AtveroEmailFiling.Models.AzureTables;
using FluentResults;
using Microsoft.Graph;
using Microsoft.Graph.Models;

namespace AtveroEmailFiling.Services.GraphApiService.Emails
{
    /// <summary>
    /// Interface for interacting with emails via the Microsoft Graph API.
    /// </summary>
    public interface IGraphEmailService
    {
        Task<Result<(List<Microsoft.Graph.Models.Message>, FilingFolder?)>> GetEmailByDelta(
            GraphServiceClient graphClient,
            string folderId,
            string folderName,
            string userName,
            string customerDomain,
            string rowId,
            string activeUser
        );

        Task<Result<List<Microsoft.Graph.Models.Message>>> GetSentItems(
            GraphServiceClient graphClient,
            string folderId,
            string activeUser,
            DateTime? lastSyncTime,
            string[]? addinManifestId
        );
        Task<Result<List<MailFolder>>> GetFilingFolders(
            GraphServiceClient graphClient,
            string activeUser
        );

        Task<Result<Dictionary<string, string>>> MapFromRestIdToPersistentIDs(
            List<string> originalIds,
            GraphServiceClient graphClient,
            string activeUser
        );
        Task<Result<Dictionary<string, string>>> MapFromEntryIdToPersistentIDs(
            List<string> originalIds,
            GraphServiceClient graphClient,
            string activeUser
        );
        Task<Result<Dictionary<string, string>>> MapFromPersistentIDs(
            List<string> restIds,
            GraphServiceClient graphClient,
            string activeUser
        );
    }
}
