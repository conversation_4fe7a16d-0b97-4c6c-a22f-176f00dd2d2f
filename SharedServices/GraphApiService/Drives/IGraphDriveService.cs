﻿using FluentResults;
using Microsoft.Graph;

namespace AtveroEmailFiling.Services.GraphApiService.Drives
{
    public interface IGraphDriveService
    {
        Task<Result<string>> GetDriveIdFromProjectCodeAsync(
            string projectCode,
            bool confidential,
            GraphServiceClient graphClient
        );

        Task<Result<string>> GetDriveIdFromSitePathAsync(
            string sitePath,
            bool confidential,
            GraphServiceClient graphClient
        );
    }
}
