﻿using Newtonsoft.Json;

namespace AtveroEmailFiling.Services.GraphApiService.ApiModels
{
    /// <summary>
    /// Represents the response from a search query, containing multiple search values.
    /// </summary>
    public class SearchResponse
    {
        /// <summary>
        /// Gets or sets the list of search values.
        /// </summary>
        [JsonProperty("value")]
        public List<SearchValue>? Value { get; set; }
    }

    /// <summary>
    /// Represents a search value containing search terms and hits.
    /// </summary>
    public class SearchValue
    {
        /// <summary>
        /// Gets or sets the search terms used in the query.
        /// </summary>
        [JsonProperty("searchTerms")]
        public List<string>? SearchTerms { get; set; }

        /// <summary>
        /// Gets or sets the list of hits containers.
        /// </summary>
        [JsonProperty("hitsContainers")]
        public List<HitsContainer>? HitsContainers { get; set; }
    }

    /// <summary>
    /// Represents a container for search hits, including total hits and whether more results are available.
    /// </summary>
    public class HitsContainer
    {
        /// <summary>
        /// Gets or sets the list of hits.
        /// </summary>
        [JsonProperty("hits")]
        public List<Hit>? Hits { get; set; }

        /// <summary>
        /// Gets or sets the total number of hits.
        /// </summary>
        [JsonProperty("total")]
        public int Total { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether more results are available.
        /// </summary>
        [JsonProperty("moreResultsAvailable")]
        public bool MoreResultsAvailable { get; set; }
    }

    /// <summary>
    /// Represents a single search hit with associated details such as ID, rank, summary, and resource.
    /// </summary>
    public class Hit
    {
        /// <summary>
        /// Gets or sets the ID of the hit.
        /// </summary>
        [JsonProperty("hitId")]
        public string? HitId { get; set; }

        /// <summary>
        /// Gets or sets the rank of the hit.
        /// </summary>
        [JsonProperty("rank")]
        public int Rank { get; set; }

        /// <summary>
        /// Gets or sets the summary of the hit.
        /// </summary>
        [JsonProperty("summary")]
        public string? Summary { get; set; }

        /// <summary>
        /// Gets or sets the resource associated with the hit.
        /// </summary>
        [JsonProperty("resource")]
        public Resource? Resource { get; set; }
    }

    /// <summary>
    /// Represents a resource in a search hit, including details such as type, description, and name.
    /// </summary>
    public class Resource
    {
        /// <summary>
        /// Gets or sets the OData type of the resource.
        /// </summary>
        [JsonProperty("@odata.type")]
        public string? ODataType { get; set; }

        /// <summary>
        /// Gets or sets the description of the resource.
        /// </summary>
        [JsonProperty("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Gets or sets the name of the resource.
        /// </summary>
        [JsonProperty("name")]
        public string? Name { get; set; }

        [JsonProperty("Id")]
        public string? Id { get; set; }
    }
}
