﻿using Newtonsoft.Json;

namespace AtveroEmailFiling.Services.GraphApiService.ApiModels
{
    /// <summary>
    /// Represents the response from a site query, containing detailed information about a site.
    /// </summary>
    public class SiteResponse
    {
        /// <summary>
        /// Gets or sets the OData context of the response.
        /// </summary>
        [JsonProperty("@odata.context")]
        public string? ODataContext { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the site was created.
        /// </summary>
        [JsonProperty("createdDateTime")]
        public DateTime? CreatedDateTime { get; set; }

        /// <summary>
        /// Gets or sets the description of the site.
        /// </summary>
        [JsonProperty("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier of the site.
        /// </summary>
        [JsonProperty("id")]
        public string? Id { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the site was last modified.
        /// </summary>
        [JsonProperty("lastModifiedDateTime")]
        public DateTime? LastModifiedDateTime { get; set; }

        /// <summary>
        /// Gets or sets the name of the site.
        /// </summary>
        [JsonProperty("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Gets or sets the web URL of the site.
        /// </summary>
        [JsonProperty("webUrl")]
        public string? WebUrl { get; set; }

        /// <summary>
        /// Gets or sets the display name of the site.
        /// </summary>
        [JsonProperty("displayName")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// Gets or sets the root object of the site.
        /// </summary>
        [JsonProperty("root")]
        public object? Root { get; set; }

        /// <summary>
        /// Gets or sets the site collection information of the site.
        /// </summary>
        [JsonProperty("siteCollection")]
        public SiteCollection? SiteCollection { get; set; }
    }

    /// <summary>
    /// Represents the site collection information of a site.
    /// </summary>
    public class SiteCollection
    {
        /// <summary>
        /// Gets or sets the hostname of the site collection.
        /// </summary>
        [JsonProperty("hostname")]
        public string? Hostname { get; set; }
    }
}
