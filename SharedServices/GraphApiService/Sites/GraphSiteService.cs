﻿using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.GraphApiService.ApiModels;
using AtveroEmailFiling.Services.GraphApiService.Sites;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Microsoft.Graph.Search.Query;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Services.GraphApiService
{
    public class GraphSiteService : IGraphSiteService
    {
        private readonly IGraphApiClient _graphApiClient;
        private readonly ILogger<GraphSiteService> _logger;

        public GraphSiteService(IGraphApiClient graphApiClient, ILogger<GraphSiteService> logger)
        {
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<List<Project>> GetAssociatedSitesAsync(
            string hubSiteId,
            string? search,
            GraphServiceClient graphClient
        )
        {
            _logger.LogInformation(
                "Starting GetAssociatedSitesAsync with hubSiteId: {hubSiteId}",
                hubSiteId
            );

            try
            {
                var queryString = BuildQueryString(hubSiteId, search);

                var requestBody = new QueryPostRequestBody
                {
                    Requests = new List<SearchRequest>
                    {
                        new SearchRequest
                        {
                            EntityTypes = new List<EntityType?> { EntityType.Site },
                            Fields = new List<string>
                            {
                                "DepartmentId",
                                "sitename",
                                "name",
                                "description",
                                "Title",
                                "URL",
                                "CreatedDate",
                                "LastModifiedDate",
                                "Owner",
                                "Template",
                                "ContentType",
                                "SiteCollectionId",
                                "WebTemplate",
                                "SiteId",
                            },
                            SortProperties = new List<SortProperty>
                            {
                                new SortProperty { Name = "Name", IsDescending = false },
                            },
                            Query = new SearchQuery { QueryString = queryString },
                        },
                    },
                };

                _logger.LogInformation("Sending search query to Graph API.");
                ApiModels.SearchResponse? searchResponse = await _graphApiClient.SearchSitesAsync(
                    requestBody,
                    graphClient
                );

                if (searchResponse != null)
                {
                    _logger.LogInformation("Extracting projects from search response.");
                    return ExtractProjects(hubSiteId, searchResponse);
                }
                else
                {
                    throw new Exception("Failed to get project list");
                }
            }
            catch (HttpRequestException httpEx)
            {
                _logger.LogError(
                    httpEx,
                    "HTTP request error occurred while getting associated sites."
                );
                throw new InvalidOperationException(
                    "A network-related error occurred while processing the request.",
                    httpEx
                );
            }
            catch (JsonException jsonEx)
            {
                _logger.LogError(jsonEx, "JSON error occurred while deserializing the response.");
                throw new InvalidOperationException(
                    "An error occurred while processing the response data.",
                    jsonEx
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error occurred while getting associated sites.");
                throw;
            }
        }

        private string BuildQueryString(string hubSiteId, string? search)
        {
            return search + $"* DepartmentId:{{{hubSiteId}}}";
        }

        private List<Project> ExtractProjects(
            string hubSiteId,
            ApiModels.SearchResponse searchResponse
        )
        {
            if (searchResponse == null || searchResponse.Value == null)
            {
                _logger.LogError("Search response is null or does not contain any values.");
                return [];
            }

            var projects = new List<Project>();

            bool moreResults =
                searchResponse.Value[0].HitsContainers?[0].MoreResultsAvailable ?? false;
            if (searchResponse.Value[0]?.HitsContainers?[0].Total == 0 && !moreResults)
            {
                _logger.LogInformation("No projects found.");
                return [];
            }
            projects.AddRange(
                searchResponse
                    .Value.SelectMany(value => value.HitsContainers)
                    .SelectMany(hitsContainer => hitsContainer.Hits)
                    .Where(hit =>
                        !hit.HitId.Contains(hubSiteId, StringComparison.OrdinalIgnoreCase)
                    )
                    .Select(hit => new Project
                    {
                        Id = hit.Resource.Id,
                        ProjectCode = hit.Resource.Name,
                        Rank = hit.Rank,
                        ProjectTitle = hit.Resource.Description ?? hit.Resource.Name,
                    })
            );

            _logger.LogInformation("{Count} projects found.", projects.Count);
            return projects.OrderBy(p => p.ProjectCode).Take(100).ToList();
        }
    }
}
