﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>

    <OutputType>Library</OutputType>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <ItemGroup>

    <PackageReference Include="FluentResults" Version="3.16.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />

    <PackageReference Include="Microsoft.Graph" Version="5.62.0" />

    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />


  </ItemGroup>

</Project>