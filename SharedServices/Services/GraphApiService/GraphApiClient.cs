using System.Net.Http.Headers;
using System.Text;
using FluentResults;
using Microsoft.Extensions.Configuration;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Microsoft.Graph.Search.Query;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Services.GraphApiService
{
    public class GraphApiClient : IGraphApiClient
    {
        // public const string DefaultExpansion = "fields($select=*,Guid)";
        public const string DefaultExpansion = "fields($select=*,Guid)";
        private readonly IConfiguration _configuration;
        private const string DefaultLibraryName = "Filed Email Content";
        private const string DefaultConfidentialLibraryName = "Confidential Filed Email Content";

        public GraphApiClient(IConfiguration configuration)
        {
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        public async Task<Result<Message>> GetEmailByIdAsync(
            string emailId,
            GraphServiceClient graphClient,
            string activeUser
        )
        {
            try
            {
                Message? email = await graphClient.Users[activeUser].Messages[emailId].GetAsync();
                if (email != null)
                {
                    return Result.Ok(email);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(
                    $"An error occurred: {ex.Message} accessing a message in a shared mailbox"
                );
            }
            return Result.Fail("Unable to retrieve email from shared mailbox");
        }

        public async Task<Result<Message>> GetEmailByInternetIdAsync(
            string internetMessageID,
            GraphServiceClient graphClient,
            string activeUser
        )
        {
            try
            {
                Microsoft.Graph.Models.MessageCollectionResponse? emails;

                emails = await graphClient
                    .Users[activeUser]
                    .Messages.GetAsync(
                        (requestConfiguration) =>
                        {
                            requestConfiguration.QueryParameters.Filter =
                                $"internetMessageID eq '{internetMessageID}'";
                        }
                    );

                if (emails != null)
                {
                    if (emails.Value?.Count > 0)
                    {
                        return Result.Ok(emails.Value[0]);
                    }
                }

                return Result.Fail(
                    $"Message with InternetMessagesId {internetMessageID} not found"
                );
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }
            return Result.Fail("Unable to retrieve email");
        }

        public async Task<Stream?> GetMessageContentAsync(
            string messageId,
            GraphServiceClient graphClient,
            string activeUser
        )
        {
            return await graphClient.Users[activeUser].Messages[messageId].Content.GetAsync();
        }

        public async Task<DriveItem?> UploadFileAsync(
            string driveId,
            string path,
            Stream content,
            GraphServiceClient graphClient
        )
        {
            return await graphClient
                .Drives[driveId]
                .Root.ItemWithPath(path)
                .Content.PutAsync(content);
        }

        public async Task DeleteFileAsync(
            string driveId,
            string path,
            GraphServiceClient graphClient
        )
        {
            await graphClient.Drives[driveId].Root.ItemWithPath(path).DeleteAsync();
        }

        public async Task<ListItem?> GetListItemAsync(
            string driveId,
            string itemId,
            GraphServiceClient graphClient
        )
        {
            return await graphClient
                .Drives[driveId]
                .Items[itemId]
                .ListItem.GetAsync(x =>
                {
                    x.QueryParameters.Select = new[] { "id", "parentReference", "sharepointIds" };
                });
        }

        public async Task<bool> PatchListItemFieldsAsync(
            string siteId,
            string listId,
            string itemId,
            FieldValueSet fields,
            GraphServiceClient graphClient
        )
        {
            try
            {
                await graphClient
                    .Sites[siteId]
                    .Lists[listId]
                    .Items[itemId]
                    .Fields.PatchAsync(fields);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<Result<List<Message>>> GetEmailsInConversationAsync(
            string conversationId,
            GraphServiceClient graphClient,
            string activeUser
        )
        {
            List<Message> messages = new List<Message>();

            try
            {
                MessageCollectionResponse? initialRequest;

                initialRequest = await graphClient
                    .Users[activeUser]
                    .Messages.GetAsync(requestConfiguration =>
                    {
                        requestConfiguration.QueryParameters.Filter =
                            $"conversationId eq '{conversationId}'";
                        // leaving this here as it didn't work, but might in the future
                        // requestConfiguration.QueryParameters.Select =
                        //     ["*", "immutableId"];
                        requestConfiguration.Headers.Add(
                            "Prefer",
                            "outlook.body-content-type=\"text\""
                        );
                    });

                if (initialRequest?.Value?.Count < 1)
                {
                    // retry using Me
                    initialRequest = await graphClient.Me.Messages.GetAsync(requestConfiguration =>
                    {
                        requestConfiguration.QueryParameters.Filter =
                            $"conversationId eq '{conversationId}'";
                        // leaving this here as it didn't work, but might in the future
                        // requestConfiguration.QueryParameters.Select =
                        //     ["*", "immutableId"];
                        requestConfiguration.Headers.Add(
                            "Prefer",
                            "outlook.body-content-type=\"text\""
                        );
                    });
                }

                if (initialRequest?.Value?.Count > 0)
                {
                    var pageIterator = PageIterator<
                        Message,
                        MessageCollectionResponse
                    >.CreatePageIterator(
                        graphClient,
                        initialRequest,
                        (msg) =>
                        {
                            messages.Add(msg);
                            return true;
                        },
                        (req) =>
                        {
                            req.Headers.Add("Prefer", "outlook.body-content-type=\"text\"");
                            return req;
                        }
                    );

                    await pageIterator.IterateAsync();
                    return Result.Ok(messages);
                }
                else
                {
                    return Result.Fail("Didn't find any messages in this conversation");
                }
            }
            catch (Exception ex)
            {
                return Result.Fail("Failed to fetch messages " + ex.Message);
            }
        }

        public async Task<Site?> GetSiteAsync(string sitePathUrl, GraphServiceClient graphClient)
        {
            return await graphClient.Sites[sitePathUrl].GetAsync();
        }

        public async Task<DriveCollectionResponse?> GetSiteDrivesAsync(
            string siteId,
            GraphServiceClient graphClient
        )
        {
            return await graphClient.Sites[siteId].Drives.GetAsync();
        }

        // only works in non multi-geo sites
        public async Task<string?> GetSpTenantNameAsync(GraphServiceClient graphClient)
        {
            var rootSite = await graphClient.Sites["root"].GetAsync();
            if (rootSite?.SiteCollection?.Hostname == null)
            {
                throw new InvalidOperationException(
                    "Root site or site collection hostname is null."
                );
            }

            return SpTenantFromSiteUrl(rootSite.SiteCollection.Hostname);
        }

        public async Task<ApiModels.SearchResponse?> SearchSitesAsync(
            QueryPostRequestBody requestBody,
            GraphServiceClient graphClient
        )
        {
            var result = await graphClient.Search.Query.PostAsQueryPostResponseAsync(requestBody);
            var json = JsonConvert.SerializeObject(result);
            return JsonConvert.DeserializeObject<ApiModels.SearchResponse>(json);
        }

        private static string? SpTenantFromSiteUrl(string siteUrl)
        {
            if (string.IsNullOrEmpty(siteUrl))
            {
                return null;
            }

            var parts = siteUrl.Split('.');
            return parts.Length > 0 ? parts[0] : null;
        }

        public async Task<Result<string>> GetDriveIdFromProjectCodeAsync(
            string projectCode,
            bool confidential,
            GraphServiceClient graphClient
        )
        {
            if (string.IsNullOrEmpty(projectCode))
            {
                throw new ArgumentException(
                    "Project code cannot be null or empty.",
                    nameof(projectCode)
                );
            }

            // fall back here to non multi-geo with one root site
            var spTenant = await GetSpTenantNameAsync(graphClient);
            var sitePathUrl = $"{spTenant}.sharepoint.com:/sites/{projectCode}";

            var site = await graphClient.Sites[sitePathUrl].GetAsync();

            if (site == null)
            {
                return Result.Fail($"Site not found for project code: {projectCode}");
            }

            DriveCollectionResponse? drives = await graphClient.Sites[site.Id].Drives.GetAsync();

            if (drives?.Value?.Count == 0)
            {
                return Result.Fail("No drives found for the specified site.");
            }

            string libraryName = confidential
                ? DefaultConfidentialLibraryName
                : _configuration["LibraryName"] ?? DefaultLibraryName;
            var drive = drives?.Value?.FirstOrDefault(d =>
                d.Name != null && d.Name.Equals(libraryName, StringComparison.OrdinalIgnoreCase)
            );

            if (drive == null)
            {
                return Result.Fail($"Drive not found for library name: {libraryName}");
            }

            if (drive.Id != null)
                return Result.Ok(drive.Id);
            else
                return Result.Fail("Drive had no ID");
        }

        public async Task<Result<string>> GetDriveIdFromSitePathAsync(
            string sitePath,
            bool confidential,
            GraphServiceClient graphClient
        )
        {
            if (string.IsNullOrEmpty(sitePath))
            {
                return Result.Fail("Site path cannot be null or empty.");
            }

            sitePath = sitePath.Replace("https://", "");
            sitePath = sitePath.Replace("sharepoint.com/", "sharepoint.com:/");

            Console.WriteLine("looking for drives from " + sitePath);
            var site = await graphClient.Sites[sitePath].GetAsync();

            if (site == null)
            {
                return Result.Fail($"Site not found for project code: {sitePath}");
            }

            DriveCollectionResponse? drives = await graphClient.Sites[site.Id].Drives.GetAsync();

            if (drives?.Value?.Count == 0)
            {
                Console.WriteLine("No drives found for the specified site.");
                return Result.Fail("No drives found for the specified site.");
            }

            string libraryName = confidential
                ? DefaultConfidentialLibraryName
                : _configuration["LibraryName"] ?? DefaultLibraryName;
            var drive = drives?.Value?.FirstOrDefault(d =>
                d.Name != null && d.Name.Equals(libraryName, StringComparison.OrdinalIgnoreCase)
            );

            if (drive == null)
            {
                return Result.Fail($"Drive not found for library name: {libraryName}");
            }

            if (drive.Id != null)
                return Result.Ok(drive.Id);
            else
                return Result.Fail("Drive had no ID");
        }

        public async Task PatchMessageAsync(
            string messageId,
            Message message,
            GraphServiceClient graphClient,
            string activeUser
        )
        {
            await graphClient.Users[activeUser].Messages[messageId].PatchAsync(message);
        }

        public async Task<DriveItem?> GetDriveItemByPathAsync(
            string driveId,
            string path,
            GraphServiceClient graphClient
        )
        {
            return await graphClient.Drives[driveId].Root.ItemWithPath(path).GetAsync();
        }

        public async Task<string?> GetUserDisplayNameAsync(GraphServiceClient graphClient)
        {
            var user = await graphClient.Me.GetAsync();
            return user?.DisplayName;
        }

        public async Task<DriveCollectionResponse?> GetUserDrivesAsync(
            GraphServiceClient graphClient
        )
        {
            return await graphClient.Me.Drives.GetAsync();
        }

        public async Task<Drive?> GetOneDriveAsync(GraphServiceClient graphClient)
        {
            var drives = await GetUserDrivesAsync(graphClient);
            if (drives?.Value?.Count > 0)
            {
                return drives?.Value?.FirstOrDefault(drive =>
                    drive.Name != null
                    && drive.Description != null
                    && drive.Name.Equals("OneDrive", StringComparison.OrdinalIgnoreCase)
                    && !drive.Description.Equals(
                        "Personal Cache List",
                        StringComparison.OrdinalIgnoreCase
                    )
                );
            }
            else
                return null;
        }

        public async Task<DriveItem?> GetSpecialDriveItemAsync(
            string driveId,
            string specialFolder,
            GraphServiceClient graphClient
        )
        {
            return await graphClient.Drives[driveId].Special[specialFolder].GetAsync();
        }

        public async Task<DriveItemCollectionResponse?> GetDriveItemChildrenAsync(
            string driveId,
            string itemId,
            GraphServiceClient graphClient
        )
        {
            return await graphClient.Drives[driveId].Items[itemId].Children.GetAsync();
        }

        public async Task<DriveItemCollectionResponse?> GetFolderChildrenAsync(
            string driveId,
            string folderId,
            GraphServiceClient graphClient
        )
        {
            return await graphClient.Drives[driveId].Items[folderId].Children.GetAsync();
        }

        public async Task<Result<DriveItem>> GetFolderByNameAsync(
            string driveId,
            string parentId,
            string folderName,
            GraphServiceClient graphClient
        )
        {
            // Get the children of the specified parent item
            DriveItemCollectionResponse? driveItemChildren = await GetDriveItemChildrenAsync(
                driveId,
                parentId,
                graphClient
            );

            if (driveItemChildren == null)
            {
                return Result.Fail("Failed to find folder");
            }

            // Find the folder by name
            DriveItem? folder = driveItemChildren.Value?.FirstOrDefault(item =>
                item.Name != null
                && item.Name.Equals(folderName, StringComparison.OrdinalIgnoreCase)
                && item.Folder != null
            );

            if (folder != null)
            {
                return Result.Ok(folder);
            }

            return Result.Fail("Failed to find folder");
        }

        public async Task<Stream?> GetFileContentAsync(
            string driveId,
            string fileId,
            GraphServiceClient graphClient
        )
        {
            // Get the content of the file
            var fileContentStream = await graphClient
                .Drives[driveId]
                .Items[fileId]
                .Content.GetAsync();
            return fileContentStream;
        }

        public async Task CreateEmptyFileDirectlyAsync(
            string fileName,
            string token,
            string parentId,
            string driveId,
            GraphServiceClient graphClient
        )
        {
            const string GraphApiBaseUrl =
                "https://graph.microsoft.com/v1.0/me/drive/special/approot:/";
            const string JsonMediaType = "application/json";
            const string EmptyJsonArray = "[]";

            try
            {
                HttpClient _httpClient = new HttpClient();
                var newFile = new Microsoft.Graph.Models.DriveItem
                {
                    Name = fileName,
                    File = new Microsoft.Graph.Models.FileObject(), // Assuming FileObject is the correct type
                };

                var createdFile = await graphClient
                    .Drives[driveId]
                    .Items[parentId]
                    .Children.PostAsync(newFile);

                var requestUrl = $"{GraphApiBaseUrl}{fileName}:/content";
                var request = new HttpRequestMessage(HttpMethod.Put, requestUrl)
                {
                    Headers = { Authorization = new AuthenticationHeaderValue("Bearer", token) },
                    Content = new StringContent(EmptyJsonArray, Encoding.UTF8, JsonMediaType),
                };

                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<Result<DriveItem>> UploadFileToFolderAsync(
            string fileName,
            Stream fileContentStream,
            string driveId,
            string folderId,
            GraphServiceClient graphClient
        )
        {
            try
            {
                // Upload the file content to the specified folder
                var driveItem = await graphClient
                    .Drives[driveId]
                    .Items[folderId]
                    .ItemWithPath(fileName)
                    .Content.PutAsync(fileContentStream);

                if (driveItem != null)
                {
                    // Return the uploaded DriveItem
                    return Result.Ok(driveItem);
                }
                else
                {
                    return Result.Fail("Failed up upload message");
                }
            }
            catch (Exception)
            {
                return Result.Fail("Failed up upload message");
            }
        }

        public async Task<DriveItem?> CreateAppFolderAsync(
            string driveId,
            string parentId,
            string appFolderName,
            GraphServiceClient graphClient
        )
        {
            try
            {
                // Define the folder to create
                var folderToCreate = new DriveItem
                {
                    Name = appFolderName, // Replace with the actual app folder name or make it a parameter
                    Folder = new Folder(),
                };

                // Create the folder in the specified drive and parent directory
                var createdFolder = await graphClient
                    .Drives[driveId]
                    .Items[parentId]
                    .Children.PostAsync(folderToCreate);

                // Return the created DriveItem representing the new folder
                return createdFolder;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<HttpResponseMessage?> GetEmailDelta(string token)
        {
            const string GraphApiBaseUrl =
                "https://graph.microsoft.com/v1.0/me/mailFolders/inbox/messages/delta";
            // const string JsonMediaType = "application/json";
            //  const string EmptyJsonArray = "[]";

            try
            {
                HttpClient _httpClient = new HttpClient();

                var request = new HttpRequestMessage(HttpMethod.Get, GraphApiBaseUrl)
                {
                    Headers = { Authorization = new AuthenticationHeaderValue("Bearer", token) },
                    //    Content = new StringContent(EmptyJsonArray, Encoding.UTF8, JsonMediaType),
                };

                var response = await _httpClient.SendAsync(request);

                //response.EnsureSuccessStatusCode();
                return response;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<Result<ListItem>> GetById(
            string listname,
            string id,
            string siteId,
            GraphServiceClient graphClient
        )
        {
            try
            {
                ListItem? item = await graphClient
                    .Sites[siteId]
                    .Lists[listname]
                    .Items[id]
                    .GetAsync(requestConfiguration =>
                    {
                        requestConfiguration.QueryParameters.Expand = ["Fields"];
                    });
                ListItem? guidItem = await graphClient
                    .Sites[siteId]
                    .Lists[listname]
                    .Items[id]
                    .GetAsync(requestConfiguration =>
                    {
                        requestConfiguration.QueryParameters.Expand = [DefaultExpansion];
                    });
                if (item != null)
                {
                    if (null != guidItem)
                    {
                        item.Fields.AdditionalData["GUID"] = guidItem
                            ?.Fields
                            ?.AdditionalData["GUID"];
                    }
                    return Result.Ok(item);
                }
                else
                {
                    return Result.Fail("Unable to fetch specified item");
                }
            }
            catch (Exception ex)
            {
                return Result.Fail(
                    new Error(
                        $"GetById - Exception fetching item {siteId} {id} in {listname}"
                    ).CausedBy(ex)
                );
            }
        }

        public async Task<Result<List<ListItem>>> Find(
            string listname,
            string siteId,
            GraphServiceClient graphClient
        )
        {
            return await Find(listname, "", siteId, graphClient);
        }

        public async Task<Result<List<ListItem>>> Find(
            string listname,
            string filter,
            string siteId,
            GraphServiceClient graphClient
        )
        {
            try
            {
                ListItemCollectionResponse items;
                ListItemCollectionResponse guidItems;
                if (string.IsNullOrEmpty(filter))
                {
                    items = await graphClient
                        .Sites[siteId]
                        .Lists[listname]
                        .Items.GetAsync(requestConfiguration =>
                        {
                            requestConfiguration.QueryParameters.Expand = ["Fields"];
                        });

                    guidItems = await graphClient
                        .Sites[siteId]
                        .Lists[listname]
                        .Items.GetAsync(requestConfiguration =>
                        {
                            requestConfiguration.QueryParameters.Expand = [DefaultExpansion];
                        });
                }
                else
                {
                    items = await graphClient
                        .Sites[siteId]
                        .Lists[listname]
                        .Items.GetAsync(requestConfiguration =>
                        {
                            requestConfiguration.QueryParameters.Expand = ["Fields"];
                            requestConfiguration.QueryParameters.Filter = filter;
                        });
                    guidItems = await graphClient
                        .Sites[siteId]
                        .Lists[listname]
                        .Items.GetAsync(requestConfiguration =>
                        {
                            requestConfiguration.QueryParameters.Expand = [DefaultExpansion];
                            requestConfiguration.QueryParameters.Filter = filter;
                        });
                }

                Dictionary<string, string> idGuidLookup = new Dictionary<string, string>();
                if (guidItems?.Value != null)
                {
                    List<ListItem> mappedItems = new List<ListItem>();
                    var pageIterator = PageIterator<
                        ListItem,
                        ListItemCollectionResponse
                    >.CreatePageIterator(
                        graphClient,
                        guidItems,
                        // Callback executed for each item in
                        // the collection
                        (item) =>
                        {
                            idGuidLookup.Add(
                                item.Id,
                                item.Fields.AdditionalData["GUID"].ToString()
                            );

                            return true;
                        }
                    );

                    await pageIterator.IterateAsync();
                }

                if (items?.Value != null)
                {
                    List<ListItem> mappedItems = new List<ListItem>();
                    var pageIterator = PageIterator<
                        ListItem,
                        ListItemCollectionResponse
                    >.CreatePageIterator(
                        graphClient,
                        items,
                        // Callback executed for each item in
                        // the collection
                        (item) =>
                        {
                            if (idGuidLookup.ContainsKey(item.Id))
                            {
                                item.Fields.AdditionalData["GUID"] = idGuidLookup[item.Id];
                            }
                            mappedItems.Add(item);

                            return true;
                        }
                    );

                    await pageIterator.IterateAsync();

                    return Result.Ok(mappedItems);
                }
                else
                {
                    return Result.Fail(
                        $"Find - Failed to fetch items in {siteId} {listname} {filter}"
                    );
                }
            }
            catch (Exception ex)
            {
                return Result.Fail(new Error("Exception finding items").CausedBy(ex));
            }
        }

        public async Task<Result<ListItem>> CreateListItem(
            ListItem item,
            string list,
            string siteId,
            GraphServiceClient graphClient
        )
        {
            try
            {
                ListItem? createdItem = await graphClient
                    .Sites[siteId]
                    .Lists[list]
                    .Items.PostAsync(item);
                // fetch again to get the GUID


                return Result.Ok(createdItem);
            }
            catch (Exception ex)
            {
                return Result.Fail(new Error("Failed to create item").CausedBy(ex));
            }
        }

        public async Task<Result<ListItem>> UpdateListItem(
            ListItem item,
            string list,
            string siteId,
            GraphServiceClient graphClient
        )
        {
            try
            {
                ListItem? createdItem = await graphClient
                    .Sites[siteId]
                    .Lists[list]
                    .Items[item.Id]
                    .PatchAsync(item);
                // fetch again to get the GUID


                return Result.Ok(createdItem);
            }
            catch (Exception ex)
            {
                return Result.Fail(new Error("Failed to create item").CausedBy(ex));
            }
        }

        public async Task<Result> DeleteListItem(
            string itemId,
            string list,
            string siteId,
            GraphServiceClient graphClient
        )
        {
            try
            {
                await graphClient.Sites[siteId].Lists[list].Items[itemId].DeleteAsync();
                return Result.Ok();
            }
            catch (Exception e)
            {
                return Result.Fail(new Error("Failed to delete " + itemId).CausedBy(e));
            }
        }
    }
}
