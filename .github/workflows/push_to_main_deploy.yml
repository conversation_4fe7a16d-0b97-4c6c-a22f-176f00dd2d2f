name: Push main to dev and staging on cmap.tech

on:
  # Runs on pushes targeting the default branch
  push:
    branches: ["main"]

env:
  AZURE_FUNCTIONAPP_NAME: atvero-fa-emailfiling-dev # set this to your application's name
  AZURE_STAGE_FUNCTIONAPP_NAME: atvero-fa-emailfiling-staging # set this to your application's name

  AZURE_DEV_CMAPMAIL_FUNCTIONAPP_NAME: atvero-fa-emailfiling-dev-001 # set this to your application's name

  AZURE_FUNCTIONAPP_PACKAGE_PATH: "AtveroEmailFilingAzure/" # set this to the path to your web app project, defaults to the repository root
  DOTNET_VERSION: "8.0.x" # set this to the dotnet version to use

jobs:

  build:
    runs-on: ubuntu-latest
  
    steps:

    - name: Install dependencies
      run: sudo apt-get update && sudo apt-get install -y libxrender1 libfontconfig libxext6 libc6 libxml2

    - uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}     


    - name: Restore dependencies
      run: |
        dotnet tool restore
        dotnet restore   

    - name: Build
      run: dotnet build --no-restore

    - name: "Resolve Project Dependencies Using Dotnet"
      shell: bash
      run: |
        pushd './${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}'
        dotnet build --configuration Release --output ./output
        popd

    - name: "Deploy to Dev"
      uses: Azure/functions-action@v1
      id: fa
      with:
        app-name: ${{ env.AZURE_FUNCTIONAPP_NAME }}
        package: "${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}/output"
        publish-profile: ${{ secrets.ATVERO_FA_EMAILFILING_DEV }}

    - name: "Deploy to Stage"
      uses: Azure/functions-action@v1
      id: fa-stage
      with:
        app-name: ${{ env.AZURE_STAGE_FUNCTIONAPP_NAME }}
        package: "${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}/output"
        publish-profile: ${{ secrets.ATVERO_FA_EMAILFILING_STAGE }}

