name: Build and Deploy Release

on:
  release:
    types:
      - created

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 8.0.x

    - name: Restore dependencies
      run: dotnet restore AtveroEmailFilingAzure

    - name: "Create version file"
      shell: bash
      run: |
          ./createVersionFile.sh ${{ github.event.release.tag_name }}

    - name: Build
      run: dotnet build AtveroEmailFilingAzure --no-restore

    - name: Test
      run: dotnet test AtveroEmailFilingAzure --no-build --verbosity normal

    - name: Publish
      run: dotnet publish AtveroEmailFilingAzure --configuration Release --output ./publish

    - name: Deploy Atvero Mail to UK Azure Web App
      uses: azure/webapps-deploy@v2
      with:
        app-name: 'Atvero-fa-EmailFiling-Prod'
        publish-profile: ${{ secrets.ATVERO_FA_EMAILFILING_PROD }}
        package: ./publish

    - name: Deploy CMap Mail to US North Central Azure Web App
      uses: azure/webapps-deploy@v2
      with:
        app-name: 'Atvero-fa-EmailFiling-Prod-us-002'
        publish-profile: ${{ secrets.CMAPMAIL_NA_FA_EMAILFILING_PROD }}
        package: ./publish

  make_sentry_version:
    needs: [build-and-deploy]
    runs-on: ubuntu-latest
    steps:
      - name: "Make sentry release"
        env:
          # Expose the project secrets for sentry-cli as env vars
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
          SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}
        run: |
          # Install Sentry CLI
          curl -sL https://sentry.io/get-cli/ | bash

          # Create new Sentry release
          sentry-cli releases new --finalize -p atvero-mail-backend "${{ github.event.release.tag_name }}"
