name: Deploy Release API Documentation

on:
  release:
    types: [published]

jobs:
  deploy-api-docs:
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install documentation tools
      run: |
        npm install -g @redocly/cli

    - name: Validate API specification
      run: |
        npx @redocly/cli lint API/ApiDefinition.yaml

    - name: Generate HTML documentation
      run: |
        mkdir -p docs-output
        npx @redocly/cli build-docs API/ApiDefinition.yaml --output=docs-output/api-docs.html

    - name: Create wiki documentation
      run: |
        mkdir -p wiki-content

        # Copy API specification
        cp API/ApiDefinition.yaml wiki-content/

        # Create wiki home page with release information
        cat > wiki-content/Home.md << 'EOF'
        # 🚀 Atvero Email Filing API Documentation

        **Release ${{ github.event.release.tag_name }}** - Published on ${{ github.event.release.published_at }}

        ## 📋 Release Information

        | Property | Value |
        |----------|-------|
        | **Version** | ${{ github.event.release.tag_name }} |
        | **Released** | ${{ github.event.release.published_at }} |
        | **Commit** | `${{ github.sha }}` |
        | **Repository** | [${{ github.repository }}](${{ github.event.repository.html_url }}) |

        ## 📚 Documentation

        ### 🔗 Quick Navigation

        - 📖 **[Interactive API Documentation](API-Documentation)** - Complete interactive documentation
        - 📄 **[OpenAPI Specification](OpenAPI-Specification)** - Raw YAML specification
        - 🏷️ **[Release Notes](${{ github.event.release.html_url }})** - What's new in this release
        - 📂 **[Source Repository](${{ github.event.repository.html_url }})** - View the source code

        ### 🎯 API Sections

        | Section | Description | Wiki Page |
        |---------|-------------|-----------|
        | 📧 **Email Filing** | File emails to SharePoint projects | [Email Filing Endpoints](Email-Filing-API) |
        | 🏢 **SharePoint** | Manage SharePoint sites and projects | [SharePoint Endpoints](SharePoint-API) |
        | 📬 **Subscriptions** | Email subscription management | [Subscription Endpoints](Subscription-API) |
        | 🔗 **Webhooks** | Microsoft Graph webhook handlers | [Webhook Endpoints](Webhook-API) |

        ## 🚀 What's New in This Release

        This release includes the latest API documentation with all endpoints, schemas, and examples.

        ### ✨ Features
        - Complete API endpoint documentation
        - Request/response schemas with validation
        - Interactive examples for testing
        - Authentication and error handling guides

        ### 🔧 Technical Details
        - Built with OpenAPI 3.0.3 specification
        - Generated using Redoc for interactive documentation
        - Automatically deployed via GitHub Actions

        ---

        > 📝 **Note**: This documentation was automatically generated from release ${{ github.event.release.tag_name }}
        >
        > For the latest development documentation, see the main branch.
        EOF

        # Create API Documentation page with embedded HTML
        cat > wiki-content/API-Documentation.md << 'EOF'
        # 📖 Interactive API Documentation

        **Release ${{ github.event.release.tag_name }}** - Interactive API Documentation

        ## 🌐 Full Interactive Documentation

        The complete interactive API documentation is available as an HTML file.

        > **Note**: GitHub Wiki doesn't support embedded HTML frames, so the interactive documentation
        > is provided as a downloadable HTML file that you can open in your browser.

        ### 📥 Download Options

        1. **[Download Interactive Documentation](../blob/main/docs/api-docs-${{ github.event.release.tag_name }}.html)** - Complete HTML file
        2. **[View OpenAPI Specification](OpenAPI-Specification)** - Raw YAML specification
        3. **[Browse API Sections](Home#-api-sections)** - Quick navigation to specific endpoints

        ## 🔍 API Overview

        The Atvero Email Filing API provides endpoints for:

        ### 📧 Email Filing Operations
        - File individual emails to SharePoint projects
        - File entire conversation threads
        - Check filing status and retrieve filing information
        - Unfile emails when needed

        ### 🏢 SharePoint Integration
        - Retrieve available hub sites and projects
        - Manage hub site permissions and rights
        - Handle custom tags and project metadata
        - Create new SharePoint sites

        ### 📬 Subscription Management
        - Create and manage Microsoft Graph subscriptions
        - Handle webhook notifications for email events
        - Manage subscription lifecycle events
        - Clear and update user subscriptions

        ### 🔗 Webhook Endpoints
        - Process incoming email notifications
        - Handle subscription lifecycle events
        - Manage webhook validation and security

        ## 🔐 Authentication

        All API endpoints require Bearer token authentication using Microsoft Graph JWT tokens.

        ```
        Authorization: Bearer <your-jwt-token>
        ```

        ## 📊 Response Format

        All API responses follow a consistent format:

        ```json
        {
          "isSuccess": boolean,
          "message": "string",
          "data": object | array | null
        }
        ```

        ## 🚀 Getting Started

        1. **Authentication**: Obtain a valid Microsoft Graph JWT token
        2. **Explore Endpoints**: Browse the [API sections](Home#-api-sections)
        3. **Test Requests**: Use the interactive documentation for testing
        4. **Integration**: Implement the API calls in your application

        ---

        > 📝 Generated from OpenAPI specification version ${{ github.event.release.tag_name }}
        EOF

        # Create OpenAPI Specification page
        cat > wiki-content/OpenAPI-Specification.md << 'EOF'
        # 📄 OpenAPI Specification

        **Release ${{ github.event.release.tag_name }}** - Raw OpenAPI YAML Specification

        ## 📋 Specification Details

        | Property | Value |
        |----------|-------|
        | **OpenAPI Version** | 3.0.3 |
        | **API Version** | 1.0.0 |
        | **Release** | ${{ github.event.release.tag_name }} |
        | **Generated** | ${{ github.event.release.published_at }} |

        ## 📥 Download Specification

        - **[Download YAML File](../blob/main/API/ApiDefinition.yaml)** - Complete OpenAPI specification
        - **[View in Repository](../blob/main/API/ApiDefinition.yaml)** - Browse in GitHub

        ## 🔧 Usage

        You can use this OpenAPI specification with various tools:

        ### 📖 Documentation Generators
        - **Redoc** - Interactive HTML documentation
        - **Swagger UI** - Interactive API explorer
        - **Postman** - Import for API testing

        ### 🛠️ Code Generation
        - **OpenAPI Generator** - Generate client SDKs
        - **Swagger Codegen** - Generate server stubs
        - **AutoRest** - Generate .NET clients

        ### ✅ Validation Tools
        - **Redocly CLI** - Lint and validate specifications
        - **Swagger Editor** - Online editor and validator
        - **Spectral** - Advanced linting and style guides

        ## 📚 Specification Structure

        The specification includes:

        ### 🎯 Core Components
        - **Paths** - All API endpoints with methods and parameters
        - **Schemas** - Request/response data models
        - **Security** - Authentication requirements
        - **Servers** - Available API environments

        ### 🏷️ Tags and Organization
        - **Email Filing** - Email filing operations
        - **SharePoint** - SharePoint integration endpoints
        - **Subscriptions** - Subscription management
        - **Webhooks** - Webhook notification handlers
        - **Email Operations** - Email-related utilities
        - **Debug** - Development and testing endpoints

        ## 🌐 API Servers

        The specification defines multiple server environments:

        | Environment | URL |
        |-------------|-----|
        | **Production** | `https://atvero-fa-emailfiling-prod.azurewebsites.net/api` |
        | **Staging** | `https://atvero-fa-emailfiling-staging.azurewebsites.net/api` |
        | **Development** | `https://atvero-fa-emailfiling-dev.azurewebsites.net/api` |

        ---

        > 📝 This specification is automatically validated and deployed with each release
        EOF

        # Copy the generated HTML documentation to docs directory for repository storage
        mkdir -p docs
        cp docs-output/api-docs.html docs/api-docs-${{ github.event.release.tag_name }}.html

    - name: Checkout wiki repository
      uses: actions/checkout@v4
      with:
        repository: ${{ github.repository }}.wiki
        path: wiki
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Deploy to wiki
      run: |
        # Copy wiki content to wiki repository
        cp -r wiki-content/* wiki/

        cd wiki

        # Configure git
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"

        # Add and commit changes
        git add .
        git commit -m "📚 Update API documentation for release ${{ github.event.release.tag_name }}" || exit 0
        git push

    - name: Commit HTML documentation to repository
      run: |
        # Add the HTML documentation to the repository
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"

        git add docs/api-docs-${{ github.event.release.tag_name }}.html
        git commit -m "📖 Add API documentation HTML for release ${{ github.event.release.tag_name }}" || exit 0
        git push

    - name: Comment on release with documentation links
      uses: actions/github-script@v7
      with:
        script: |
          const { owner, repo } = context.repo;
          const releaseId = context.payload.release.id;
          const wikiUrl = `https://github.com/${owner}/${repo}/wiki`;
          const htmlDocUrl = `https://github.com/${owner}/${repo}/blob/main/docs/api-docs-${{ github.event.release.tag_name }}.html`;

          const comment = `## 📚 API Documentation Published

          The API documentation for release **${{ github.event.release.tag_name }}** has been successfully deployed to the private GitHub Wiki:

          ### 🔗 Documentation Links
          - � **[Wiki Home](${wikiUrl})** - Main documentation hub with release information
          - � **[API Documentation](${wikiUrl}/API-Documentation)** - Complete API overview and guides
          - 📄 **[OpenAPI Specification](${wikiUrl}/OpenAPI-Specification)** - Raw YAML specification details
          - 💾 **[Interactive HTML Documentation](${htmlDocUrl})** - Downloadable interactive docs

          ### 📊 Release Details
          - **Version**: ${{ github.event.release.tag_name }}
          - **Deployed**: ${new Date().toISOString()}
          - **Commit**: \`${{ github.sha }}\`
          - **Wiki Updated**: ✅ Private repository wiki
          - **HTML Docs**: ✅ Stored in repository \`docs/\` directory

          ### 🚀 What's Included
          - **Wiki Pages**: Structured documentation with navigation
          - **Release Context**: Version-specific information and metadata
          - **API Overview**: Complete endpoint documentation and guides
          - **Interactive HTML**: Downloadable Redoc documentation
          - **OpenAPI Spec**: Raw YAML specification for tools and generators

          ### 📋 Wiki Structure
          - **Home**: Release information and quick navigation
          - **API Documentation**: Complete API overview with examples
          - **OpenAPI Specification**: Technical specification details

          The documentation is now available in the private wiki for team access!

          ---
          *📝 Documentation automatically generated and deployed via GitHub Actions*`;

          await github.rest.issues.createComment({
            owner,
            repo,
            issue_number: context.payload.release.id,
            body: comment
          });
