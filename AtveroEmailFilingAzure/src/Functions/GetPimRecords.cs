using System.Net;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using CMapPim.Model;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Newtonsoft.Json;
using Sentry.Azure.Functions.Worker;

namespace AtveroEmailFiling.Functions
{
    public class GetPimRecordsResponse
    {
        public string SiteUrl { get; set; } = string.Empty;
        public string RecordName { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    public class GetPimRecords
    {
        private readonly ILogger<GetPimRecords> _logger;
        private readonly ITokenValidationService _tokenValidationService;
        private readonly IGraphApiClient _graphApiClient;
        private readonly IGraphClientService _graphClientService;

        public GetPimRecords(
            ILogger<GetPimRecords> logger,
            ITokenValidationService tokenValidationService,
            IGraphApiClient graphApiClient,
            IGraphClientService graphClientService
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
        }

        [Function("GetPimRecords")]
        public async Task<HttpResponseData?> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            HttpResponseData response = req.CreateResponse();

            try
            {
                string? recordName = req.Query["recordName"];

                if (string.IsNullOrEmpty(recordName))
                {
                    HttpResponseData? res = await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "recordName parameter is required.",
                        _logger
                    );
                    return res;
                }

                _logger.LogInformation($"Processing request for record name: {recordName}");

                // Validate token and get user details
                Models.TokenDetails? tokenDetails = null;
                string? token =
                    await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(req);
                if (token != null)
                {
                    tokenDetails = _tokenValidationService.GetUserDetails(token);
                    _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                    SentrySdk.ConfigureScope(scope =>
                    {
                        scope.User = new SentryUser
                        {
                            Id = tokenDetails.Upn,
                            Email = tokenDetails.Upn,
                        };
                    });
                }

                if (null == tokenDetails)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Unable to extract token details from the request.",
                        _logger
                    );
                }

                string customerDomainRaw = EmailUtils.ExtractDomainFromEmailOrUpn(
                    tokenDetails.Upn,
                    _logger
                );

                _logger.LogInformation($"Customer domain: {customerDomainRaw}");

                // Get tenant information
                Result<string> tenant = await FunctionUtils.GetTenantAsync(
                    req,
                    _logger,
                    _tokenValidationService,
                    _graphApiClient,
                    _graphClientService
                );

                if (tenant.IsFailed)
                {
                    IEnumerable<Exception> ex = ApiResponseUtility.ExtractExceptionsFromResult(
                        tenant.ToResult()
                    );

                    return await ApiResponseUtility.HandleInternalError(
                        cancellationToken,
                        req,
                        ex,
                        "Unable to get SharePoint tenant.",
                        _logger
                    );
                }

                // Get Graph client
                GraphServiceClient? graphClient = _graphClientService.GetUserGraphClient(
                    token,
                    tokenDetails.ClientId,
                    tokenDetails.ClientSecret
                );
                if (graphClient == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Unable to create Graph client.",
                        _logger
                    );
                }

                // Create the site URL for the PIM records
                // Extract tenant name from the full URL returned by GetTenantAsync
                string tenantName = EmailUtils.SpTenantFromSiteUrl(tenant.Value) ?? "unknown";
                string siteUrl = $"https://{tenantName}.sharepoint.com/sites/{recordName}";

                _logger.LogInformation($"Creating Context for site: {siteUrl}");

                // Create the CMapPim Context
                Context pimContext = new Context
                {
                    SiteUrl = siteUrl,
                    Logger = _logger,
                    GraphApiClient = _graphApiClient,
                    GraphClient = graphClientResult.Value,
                };

                // Create response
                var responseData = new GetPimRecordsResponse
                {
                    SiteUrl = siteUrl,
                    RecordName = recordName,
                    Message = "Context created successfully for CMapPim project",
                };

                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    responseData,
                    customerDomainRaw
                );
            }
            catch (JsonException jsonEx)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    jsonEx,
                    "JSON serialization error occurred while processing the request.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
