using System.Net;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using CMapPim.DataModel.Generated;
using CMapPim.Model;
using CMapPim.Services;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class GetPimRecords
    {
        private readonly ILogger<GetPimRecords> _logger;
        private readonly ITokenValidationService _tokenValidationService;
        private readonly IGraphApiClient _graphApiClient;
        private readonly IGraphClientService _graphClientService;
        private readonly ICMapPimService _cmapPimService;

        public GetPimRecords(
            ILogger<GetPimRecords> logger,
            ITokenValidationService tokenValidationService,
            IGraphApiClient graphApiClient,
            IGraphClientService graphClientService,
            ICMapPimService cmapPimService
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
            _cmapPimService =
                cmapPimService ?? throw new ArgumentNullException(nameof(cmapPimService));
        }

        [Function("GetPimRecords")]
        public async Task<HttpResponseData?> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            HttpResponseData response = req.CreateResponse();

            try
            {
                string? recordNameSearch = req.Query["startsWith"];
                string? siteUrl = req.Query["siteUrl"];

                if (string.IsNullOrEmpty(recordNameSearch) && string.IsNullOrEmpty(siteUrl))
                {
                    HttpResponseData? res = await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Either recordName or siteUrl parameter is required.",
                        _logger
                    );
                    return res;
                }

                _logger.LogInformation(
                    $"Processing request for record name: {recordNameSearch}, site URL: {siteUrl}"
                );

                // Validate token and get user details
                Models.TokenDetails? tokenDetails = null;
                string? token =
                    await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(req);
                if (token != null)
                {
                    tokenDetails = _tokenValidationService.GetUserDetails(token);
                    _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                    SentrySdk.ConfigureScope(scope =>
                    {
                        scope.User = new SentryUser
                        {
                            Id = tokenDetails.Upn,
                            Email = tokenDetails.Upn,
                        };
                    });
                }

                if (null == tokenDetails)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Unable to extract token details from the request.",
                        _logger
                    );
                }

                string customerDomainRaw = EmailUtils.ExtractDomainFromEmailOrUpn(
                    tokenDetails.Upn,
                    _logger
                );

                _logger.LogInformation($"Customer domain: {customerDomainRaw}");

                // Get tenant information
                Result<string> tenant = await FunctionUtils.GetTenantAsync(
                    req,
                    _logger,
                    _tokenValidationService,
                    _graphApiClient,
                    _graphClientService
                );

                if (tenant.IsFailed)
                {
                    IEnumerable<Exception> ex = ApiResponseUtility.ExtractExceptionsFromResult(
                        tenant.ToResult()
                    );

                    return await ApiResponseUtility.HandleInternalError(
                        cancellationToken,
                        req,
                        ex,
                        "Unable to get SharePoint tenant.",
                        _logger
                    );
                }

                // Get Graph client
                if (tokenDetails.ClientId == null || tokenDetails.ClientSecret == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "ClientId or ClientSecret is null.",
                        _logger
                    );
                }

                GraphServiceClient? graphClient = _graphClientService.GetUserGraphClient(
                    token,
                    tokenDetails.ClientId,
                    tokenDetails.ClientSecret
                );
                if (graphClient == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Unable to create Graph client.",
                        _logger
                    );
                }

                Site? site = await _graphApiClient.GetSiteAsync(
                    siteUrl
                        ?.Replace("https://", "")
                        ?.Replace("sharepoint.com/", "sharepoint.com:/"),
                    graphClient
                );
                if (null == site?.Id)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Unable to get site ID.",
                        _logger
                    );
                }
                // Create the CMapPim Context
                Context pimContext = new Context
                {
                    SiteId = site.Id,
                    Logger = _logger,
                    GraphApiClient = _graphApiClient,
                    GraphClient = graphClient,
                };

                Result<List<RecordSummary>> records = await _cmapPimService.GetRecordInformation(
                    pimContext,
                    recordNameSearch
                );

                if (records.IsFailed)
                {
                    IEnumerable<Exception> ex = ApiResponseUtility.ExtractExceptionsFromResult(
                        records.ToResult()
                    );

                    return await ApiResponseUtility.HandleInternalError(
                        cancellationToken,
                        req,
                        ex,
                        "Unable to get records.",
                        _logger
                    );
                }

                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    records.Value,
                    customerDomainRaw
                );
            }
            catch (JsonException jsonEx)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    jsonEx,
                    "JSON serialization error occurred while processing the request.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
