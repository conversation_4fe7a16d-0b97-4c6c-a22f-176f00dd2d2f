using AtveroEmailFiling.Models;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSCsomClientContextService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using FluentResults;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SharePoint.Client;

namespace AtveroEmailFiling.Functions
{
    public class FunctionUtils
    {
        public static async Task<Result<ClientContext>> GetClientContext(
            HttpRequestData req,
            string tenant,
            string sitePath,
            ITokenValidationService _tokenValidationService,
            ICsomClientContextService _csomClientContextService,
            ILogger _logger
        )
        {
            var token = await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(req);
            if (string.IsNullOrEmpty(token))
            {
                return Result.Fail("Can't obtain token");
            }
            _logger.LogInformation("Token successfully retrieved.");

            TokenDetails tokenDetails = _tokenValidationService.GetUserDetails(token);
            if (tokenDetails.ClientId == null || tokenDetails.ClientSecret == null)
            {
                return Result.Fail("ClientId or ClientSecret is null");
            }

            string cleanSitePath = sitePath;
            if (sitePath.StartsWith("https"))
            {
                cleanSitePath = new Uri(sitePath).AbsolutePath;
            }

            ClientContext? clientContext = await _csomClientContextService.GetClientContextAsync(
                tenant,
                cleanSitePath,
                token,
                tokenDetails.ClientId,
                tokenDetails.ClientSecret
            );

            if (clientContext != null)
            {
                return Result.Ok(clientContext);
            }
            else
            {
                return Result.Fail("Cannot get client context");
            }
        }

        public static async Task<Result<string>> GetTenantAsync(
            HttpRequestData req,
            ILogger _logger,
            ITokenValidationService _tokenValidationService,
            IGraphApiClient _graphApiClient,
            IGraphClientService _graphClientService
        )
        {
            try
            {
                var token = await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(
                    req
                );
                if (string.IsNullOrEmpty(token))
                    return Result.Fail("Unable to validate token");

                TokenDetails tokenDetails = _tokenValidationService.GetUserDetails(token);
                if (tokenDetails.ClientId == null || tokenDetails.ClientSecret == null)
                {
                    return Result.Fail("ClientId or ClientSecret is null");
                }
                // Getting GraphClient
                _logger.LogInformation("Attempting to get GraphClient.");
                var _graphClient = _graphClientService.GetUserGraphClient(
                    token,
                    tokenDetails.ClientId,
                    tokenDetails.ClientSecret
                );
                _logger.LogInformation("GraphClient successfully created.");
                if (_graphClient == null)
                {
                    return Result.Fail("GraphClient is null");
                }
                string? spTenant = await _graphApiClient.GetSpTenantNameAsync(_graphClient);

                if (spTenant != null)
                    return Result.Ok($"https://{spTenant}.sharepoint.com");
                return Result.Fail("Unable to obtain tenancy URL");
            }
            catch (Exception ex)
            {
                return Result.Fail(new Error("Error getting admin tenant").CausedBy(ex));
            }
        }
    }
}
