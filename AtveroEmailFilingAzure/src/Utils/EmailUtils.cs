using System.Globalization;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using AtveroEmailFiling.Models;
using AtveroEmailFiling.Services.FilingStatusCheck;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;

namespace AtveroEmailFiling.Utils
{
    public static class EmailUtils
    {
        /// <summary>
        /// Sanitizes the customer domain by removing all non-alphanumeric characters.
        /// </summary>

        public static bool AlreadyFiledCategories(List<string>? categories)
        {
            return categories != null
                && categories.Exists(
                    (cat) =>
                        cat.StartsWith("Filed in ")
                        || cat == AtveroEmailFilingAzure.Constants.OTHERS_TAG
                        || cat.StartsWith("Auto Filed by ")
                );
        }

        public static string SanitizeCustomerDomain(string customerDomain)
        {
            // Remove all non-alphanumeric characters
            var sanitizedDomain = new string(customerDomain.Where(char.IsLetterOrDigit).ToArray());

            // Ensure the sanitized domain starts with a letter
            if (char.IsDigit(sanitizedDomain.FirstOrDefault()))
            {
                sanitizedDomain = $"Domain{sanitizedDomain}";
            }

            // Truncate if necessary to keep the final name under 63 characters (leaving space for table base names)
            sanitizedDomain =
                sanitizedDomain.Length > 50 ? sanitizedDomain.Substring(0, 50) : sanitizedDomain;

            return sanitizedDomain;
        }

        public static string RecipientString(Recipient recipient)
        {
            if (
                !string.IsNullOrEmpty(recipient?.EmailAddress?.Name)
                && !string.IsNullOrEmpty(recipient?.EmailAddress?.Address)
            )
            {
                return $"{recipient.EmailAddress.Name} <{recipient.EmailAddress.Address}>";
            }
            else if (!string.IsNullOrEmpty(recipient?.EmailAddress?.Address))
            {
                return recipient.EmailAddress.Address;
            }
            else if (!string.IsNullOrEmpty(recipient?.EmailAddress?.Name))
            {
                return recipient.EmailAddress.Name;
            }
            else
            {
                return "";
            }
        }

        public static OutlookMailMessage CreateOutlookMailMessage(
            Microsoft.Graph.Models.Message message
        )
        {
            OutlookMailMessage outlookMessage = new OutlookMailMessage
            {
                Id = message.Id ?? "unknown_id", // should always have an ID
                Subject = message.Subject,
                ReceivedOn = message.ReceivedDateTime,
                InternetMessageId = EmailUtils.CleanMessageId(
                    message.InternetMessageId ?? "no-message-id"
                ),
                ConversationId = message.ConversationId,
                SearchRefiner = "Email",
                EmailReceived = message.ReceivedDateTime,
                // Handle nullable bool for HasAttachments
                AttachmentCount = message.HasAttachments.HasValue
                    ? (message.HasAttachments.Value ? 1 : 0) // If HasAttachments is true, set 1; if false, set 0
                    : 0, // Default to 0 if HasAttachments is null
            };

            if (message.From != null)
            {
                outlookMessage.From = RecipientString(message.From);
            }

            if (message.ToRecipients != null)
            {
                outlookMessage.To = string.Join(
                    "; ",
                    message.ToRecipients.Select(r => r.EmailAddress?.Address)
                );
            }

            if (message.CcRecipients != null)
            {
                outlookMessage.Cc = string.Join(
                    "; ",
                    message.CcRecipients.Select(r => r.EmailAddress?.Address)
                );
            }

            if (message.Body?.Content != null)
            {
                outlookMessage.TextSummary = EmailUtils.GetTextSummary(message.Body.Content);
            }

            return outlookMessage;
        }

        public static string CleanMessageId(string messageId)
        {
            return messageId
                .Replace("/", "-")
                .Replace("\\", "-")
                .Replace("#", "-")
                .Replace("?", "-");
        }

        /// <summary>
        /// Converts a string to PascalCase format.
        /// </summary>
        /// <param name="str">The input string to be converted.</param>
        /// <returns>The PascalCase formatted string or null if the input is null or empty.</returns>
        public static string PascalCase(string str)
        {
            if (string.IsNullOrEmpty(str))
            {
                return str;
            }
            else
            {
                TextInfo uk = new CultureInfo("en-GB", false).TextInfo;

                return uk.ToTitleCase(str).Replace(" ", "");
            }
        }

        /// <summary>
        /// Converts a string to snake_case format.
        /// </summary>
        /// <param name="str">The input string to be converted.</param>
        /// <returns>The snake_case formatted string or null if the input is null or empty.</returns>
        public static string SnakeCase(string str)
        {
            if (string.IsNullOrEmpty(str))
            {
                return str;
            }
            else
            {
                return str.Replace(" ", "_");
            }
        }

        /// <summary>
        /// Copies the contents of one stream to another.
        /// </summary>
        /// <param name="inputStream">The input stream to be copied.</param>
        /// <returns>A new memory stream containing the copied data.</returns>
        public static Stream CopyStream(Stream inputStream)
        {
            MemoryStream ms = new MemoryStream();

            inputStream.Seek(0, 0);
            inputStream.CopyTo(ms);
            inputStream.Seek(0, 0);

            ms.Seek(0, 0);

            return ms;
        }

        /// <summary>
        /// Cleans an email address by removing leading and trailing single quotes.
        /// </summary>
        /// <param name="email">The email address to be cleaned.</param>
        /// <returns>The cleaned email address.</returns>

        public static string? CleanEmail(string email)
        {
            if (email == null)
            {
                return email;
            }

            if (email.StartsWith('\''))
            {
                email = email.Substring(1);
            }

            if (email.EndsWith('\''))
            {
                email = email.Substring(0, email.Length - 1);
            }

            return email;
        }

        /// <summary>
        /// Sanitizes a filename for use in a SharePoint document library by replacing invalid characters with underscores.
        /// </summary>
        /// <param name="filename">The filename to be sanitized.</param>
        /// <returns>The sanitized filename.</returns>
        public static string SanitizeForSharepointDocLibrary(string filename)
        {
            TimeSpan regexTimeout = TimeSpan.FromSeconds(5);
            Regex pattern = new Regex("[]\"*<>:?/\\|]", RegexOptions.None, regexTimeout);
            return pattern.Replace(filename, "_");
        }

        /// <summary>
        /// Extracts the site name from a SharePoint site URL.
        /// </summary>
        /// <param name="siteUrl">The SharePoint site URL.</param>
        /// <returns>The site name or null if the URL is invalid.</returns>

        public static string? SiteFromSiteUrl(string siteUrl)
        {
            string[] parts = siteUrl.Split('/');
            if (parts.Length > 0)
            {
                return parts[parts.Length - 1];
            }
            return null;
        }

        /// <summary>
        /// Extracts the SharePoint tenant name from a site URL.
        /// </summary>

        public static string? SpTenantFromSiteUrl(string siteUrl)
        {
            string[] parts = siteUrl.Split('.');
            if (parts.Length > 0)
            {
                return parts.FirstOrDefault();
            }
            return null;
        }

        /// <summary>
        /// Removes all punctuation characters from a given text.
        /// </summary>

        public static string RemovePunctuation(string text)
        {
            // Filter out punctuation characters from the input text
            char[] result = text.Where(c => !char.IsPunctuation(c)).ToArray();

            // Return the filtered characters as a new string
            return new string(result);
        }

        /// <summary>
        /// Strips HTML tags from a given HTML string.
        /// </summary>
        /// <param name="html">The HTML string to be stripped of tags.</param>
        /// <returns>The plain text string without HTML tags.</returns>
        public static string StripHtml(string html)
        {
            TimeSpan regexTimeout = TimeSpan.FromSeconds(5);
            var htmlTagRegex = new Regex("<[^>]+>", RegexOptions.IgnoreCase, regexTimeout);
            string result = htmlTagRegex.Replace(html, "");

            return result;
        }

        public static string GetTextSummary(string emailBody)
        {
            // Remove HTML tags from the email body to get plain text
            string plainText = StripHtml(emailBody);

            // Return the plain text limited to 255 characters
            return plainText.Length > 255 ? plainText.Substring(0, 255) : plainText;
        }

        public static string ExtractDomainFromEmailOrUpn(string? emailOrUpn, ILogger logger)
        {
            if (string.IsNullOrWhiteSpace(emailOrUpn))
            {
                logger.LogError("Email or UPN cannot be null or empty.");
                throw new ArgumentException(
                    "Email or UPN cannot be null or empty.",
                    nameof(emailOrUpn)
                );
            }

            var parts = emailOrUpn.Split('@');
            if (parts.Length != 2)
            {
                logger.LogError("The provided email or UPN is not in a valid format.");
                throw new FormatException("The provided email or UPN is not in a valid format.");
            }

            var domain = parts[1].Trim();
            if (
                string.IsNullOrWhiteSpace(domain) || !domain.Contains('.', StringComparison.Ordinal)
            )
            {
                logger.LogError("The domain part of the email or UPN is invalid.");
                throw new FormatException("The domain part of the email or UPN is invalid.");
            }

            string sanitizedDomain = EmailUtils.SanitizeCustomerDomain(domain);
            logger.LogInformation("Sanitized domain: {SanitizedDomain}", sanitizedDomain);
            return sanitizedDomain;
        }

        public static string GenerateCRC32(string input)
        {
            using (var crc32 = new Crc32())
            {
                // Convert input string to byte array
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);

                // Compute the CRC32 hash
                byte[] hashBytes = crc32.ComputeHash(inputBytes);

                // Convert the hash bytes to a hexadecimal string
                return BitConverter.ToString(hashBytes).Replace("-", string.Empty);
            }
        }

        public static string GenerateFilingPathCheckSubject(OutlookMailMessage email, string? tag)
        {
            string finalTag = string.IsNullOrEmpty(tag) ? "General" : tag;
            string folder = Path.Combine(finalTag, $"{email.ReceivedOn:yyyy-MM}");
            string subject = string.IsNullOrEmpty(email.Subject) ? "No_Subject" : email.Subject;

            subject = EmailUtils.RemovePunctuation(subject);
            subject = subject.Replace("\t", " "); // remove tabs
            subject = EmailUtils.SanitizeForSharepointDocLibrary(subject);

            if (subject.Length > 128)
            {
                subject = subject.Substring(0, 128);
            }

            string messageId =
                email.InternetMessageId ?? email.ReceivedOn.ToString() + email.Subject;

            string crc32 = GenerateCRC32(messageId);
            string filename = $"{email.ReceivedOn:dd}-{email.ReceivedOn:HH}-{subject}-{crc32}.eml";
            string fullPath = Path.Combine(folder, filename);

            return fullPath.Length > 255 ? fullPath.Substring(0, 255) : fullPath;
        }

        public static async Task MarkConversationForFiling(
            IFilingStatusCheckService _filingStatusCheckService,
            string userName,
            string conversationId,
            string projectCode,
            string tag,
            bool confidential,
            bool important,
            string activeUser,
            string customerDomain
        )
        {
            try
            {
                AtveroEmailFiling.Models.AzureTables.Conversation newConversation =
                    new AtveroEmailFiling.Models.AzureTables.Conversation()
                    {
                        PartitionKey = activeUser,
                        RowKey = conversationId,
                        ProjectCode = projectCode,
                        Tag = tag,
                        Confidential = confidential,
                        Important = important,
                    };

                await _filingStatusCheckService.UpsertConversationAsync(
                    newConversation,
                    customerDomain
                );
            }
            catch (Exception)
            {
                //_logger.LogError("Can't add filed conversation to table, but continuing");
            }
        }

        public static string GetReleaseVersion()
        {
            string? version = Assembly.GetEntryAssembly()?.GetName().Version?.ToString();
            return string.IsNullOrEmpty(version) ? "unknown" : version;
        }

        public static string GetServiceName()
        {
            string? websiteName = System.Environment.GetEnvironmentVariable("WEBSITE_SITE_NAME");
            return string.IsNullOrEmpty(websiteName) ? "unknown" : websiteName;
        }

        public static async Task MoveFiledEmailToFolder(
            GraphServiceClient graphClient,
            string activeUser,
            string messageId,
            string destinationFolder,
            ILogger _logger
        )
        {
            // make the destination folder if needed

            _logger.LogInformation("Checking for folder " + destinationFolder);

            var mailFolders = await graphClient.Users[activeUser].MailFolders.GetAsync();

            if (mailFolders?.Value == null)
            {
                Console.WriteLine("No mail folders found.");
                // quitting here, as we should find folders
                return;
            }

            // 4. Find the folder by name (case-insensitive)
            var targetFolder = mailFolders.Value.FirstOrDefault(folder =>
                string.Equals(
                    folder.DisplayName,
                    destinationFolder,
                    StringComparison.OrdinalIgnoreCase
                )
            );

            if (targetFolder != null)
            {
                Console.WriteLine(
                    $"Found folder \"{destinationFolder}\" with ID: {targetFolder.Id}"
                );
            }
            else
            {
                Console.WriteLine($"Folder \"{destinationFolder}\" not found, so create it");

                var mailFolder = new MailFolder { DisplayName = destinationFolder };

                // 4. Send request to create the folder at root level
                targetFolder = await graphClient
                    .Users[activeUser]
                    .MailFolders //["Inbox"].ChildFolders
                    .PostAsync(mailFolder);

                if (targetFolder != null && !string.IsNullOrEmpty(targetFolder.Id))
                {
                    Console.WriteLine(
                        $"Successfully created folder \"{destinationFolder}\" with ID: {targetFolder.Id}"
                    );
                }
                else
                {
                    Console.WriteLine("Failed to create the folder. No ID was returned.");
                }

                // so we make the folder
            }

            if (targetFolder != null && !string.IsNullOrEmpty(targetFolder.Id))
            {
                // move the message to the folder

                _logger.LogInformation("Move email to " + destinationFolder);

                var requestBody =
                    new Microsoft.Graph.Users.Item.Messages.Item.Move.MovePostRequestBody
                    {
                        DestinationId = targetFolder.Id,
                    };

                var result = await graphClient
                    .Users[activeUser]
                    .Messages[messageId]
                    .Move.PostAsync(requestBody);
            }
        }
    }
}
