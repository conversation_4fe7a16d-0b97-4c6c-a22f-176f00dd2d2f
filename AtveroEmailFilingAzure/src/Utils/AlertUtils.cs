using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace AtveroEmailFiling.Utils
{
    class Body
    {
        [JsonPropertyName("type")]
        public required string Type { get; set; }

        [JsonPropertyName("text")]
        public required string Text { get; set; }

        [JsonPropertyName("style")]
        public string? Style { get; set; }

        [JsonPropertyName("wrap")]
        public bool Wrap { get; set; }
    }

    class Content
    {
        [JsonPropertyName("$schema")]
        public required string Schema { get; set; }

        [JsonPropertyName("type")]
        public required string Type { get; set; }

        [JsonPropertyName("version")]
        public required string Version { get; set; }

        [JsonPropertyName("body")]
        public required Body[] Body { get; set; }
    }

    class Attachment
    {
        [JsonPropertyName("contentType")]
        public required string ContentType { get; set; }

        [JsonPropertyName("contentUrl")]
        public string? ContentUrl { get; set; }

        [JsonPropertyName("content")]
        public required Content Content { get; set; }
    }

    class TeamsMessage
    {
        [JsonPropertyName("type")]
        public required string Type { get; set; }

        [JsonPropertyName("attachments")]
        public required Attachment[] Attachments { get; set; }
    }

    public static class AlertUtils
    {
        public static async Task SendTeamsAlert(string uri, string textMessage, string? error)
        {
            HttpClient httpClient = new HttpClient();

            Body body = new Body
            {
                Type = "TextBlock",
                Text = textMessage,
                Style = "heading",
                Wrap = true,
            };

            List<Body> bodyList = new List<Body> { body };

            if (error != null)
            {
                Body errorBody = new Body
                {
                    Type = "TextBlock",
                    Text = error,
                    Wrap = true,
                };
                bodyList.Add(errorBody);
            }
            ;

            Body[] bodyArray = bodyList.ToArray();

            Content content = new Content
            {
                Schema = "http://adaptivecards.io/schemas/adaptive-card.json",
                Type = "AdaptiveCard",
                Version = "1.2",
                Body = bodyArray,
            };

            Attachment attachment = new Attachment
            {
                ContentType = "application/vnd.microsoft.card.adaptive",
                ContentUrl = null,
                Content = content,
            };

            TeamsMessage message = new TeamsMessage
            {
                Type = "message",
                Attachments = (new List<Attachment> { attachment }).ToArray(),
            };

            string responseString = JsonSerializer.Serialize(message);

            Console.WriteLine(responseString);

            var jsonString = new StringContent(responseString, Encoding.UTF8, "application/json");

            // encoding manually as the PostAsyncAsJson did something weird

            try
            {
                HttpResponseMessage response = await httpClient.PostAsync(uri, jsonString);

                Console.WriteLine("Alert sent");
            }
            catch (Exception ex) // Non success
            {
                Console.WriteLine("An error occurred.");
                Console.WriteLine(ex.Message);
            }
        }
    }
}
