using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;

namespace AtveroEmailFiling.Utils
{
    public static class AppSettings
    {
        public static async Task<string?> GetSetting(
            IAzureTableService tableService,
            string customerDomain,
            string settingName
        )
        {
            if (settingName != null && customerDomain != null)
            {
                // get setting from table for domain

                FluentResults.Result<AppSetting>? settingRes =
                    await tableService.GetEntityAsync<AppSetting>(
                        "AppSettings",
                        customerDomain,
                        settingName,
                        ""
                    );

                if (settingRes.IsSuccess)
                {
                    return settingRes.Value.SettingValue;
                }
                else
                    return null;
            }
            else
            {
                return null;
            }
        }
    }
}
