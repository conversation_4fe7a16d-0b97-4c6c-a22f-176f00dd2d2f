using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using AtveroEmailFiling.Models;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.FilingStatusCheck;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using FluentResults;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Utils
{
    public class WebhookUtils
    {
        static string homeTenant = "9e4feb7c-cecc-4992-a8e0-c4640b3ef864";

        public static string getRefreshTokenName(string SubscriptionID)
        {
            string secretNameStart = SubscriptionID;
            return secretNameStart + "-refresh";
        }

        public static string getSharedSecretName(string SubscriptionID)
        {
            string secretNameStart = SubscriptionID;
            return secretNameStart + "-shared";
        }

        public static Result<SecretClient> GetSecretClient(
            string kvUrl,
            string clientId,
            string clientSecret
        )
        {
            ClientSecretCredential clientSecretCredential = new ClientSecretCredential(
                homeTenant,
                clientId,
                clientSecret
            );

            Uri? kvUri = new Uri(kvUrl);

            SecretClient secretClient = new SecretClient(kvUri, clientSecretCredential);
            return Result.Ok(secretClient);
        }

        public static async Task<Result<(KeyVaultSecret, KeyVaultSecret)>> GetVaultItems(
            string SubscriptionID,
            string kvUrl,
            ILogger _logger,
            string clientId,
            string clientSecret
        )
        {
            string refreshTokenName = getRefreshTokenName(SubscriptionID);
            string secretTokenName = getSharedSecretName(SubscriptionID);

            _logger.LogInformation("Processing a request with refresh " + refreshTokenName);
            _logger.LogInformation("Processing a request with secret_token " + secretTokenName);

            var clientRes = GetSecretClient(kvUrl, clientId, clientSecret);
            if (clientRes.IsFailed)
            {
                return Result.Fail("Can't access vault");
            }

            SecretClient secretClient = clientRes.Value;

            try
            {
                KeyVaultSecret? refreshToken = await secretClient.GetSecretAsync(refreshTokenName);
                KeyVaultSecret? sharedSecret = await secretClient.GetSecretAsync(secretTokenName);

                if (refreshToken == null || sharedSecret == null)
                {
                    _logger.LogError("Failed to obtain refresh token from vault");

                    return Result.Fail("Failed to obtain session items from vault");
                }
                return Result.Ok((refreshToken, sharedSecret));
            }
            catch (Exception)
            {
                _logger.LogInformation("Failed to obtain token from vault");
                return Result.Fail("Failed to obtain session items from vault");
            }
        }

        public static async Task<Result<(KeyVaultSecret, KeyVaultSecret)>> GetOldVaultItems(
            string SubscriptionID,
            string kvUrl,
            ILogger _logger,
            string clientId,
            string clientSecret
        )
        {
            string refreshTokenName = "mail-" + getRefreshTokenName(SubscriptionID);
            string secretTokenName = "mail-" + getSharedSecretName(SubscriptionID);

            _logger.LogInformation("Processing a request with refresh " + refreshTokenName);
            _logger.LogInformation("Processing a request with secret_token " + secretTokenName);

            var clientRes = GetSecretClient(kvUrl, clientId, clientSecret);
            if (clientRes.IsFailed)
            {
                return Result.Fail("Can't access vault");
            }

            SecretClient secretClient = clientRes.Value;

            try
            {
                KeyVaultSecret? refreshToken = await secretClient.GetSecretAsync(refreshTokenName);
                KeyVaultSecret? sharedSecret = await secretClient.GetSecretAsync(secretTokenName);

                if (refreshToken == null || sharedSecret == null)
                {
                    _logger.LogError("Failed to obtain refresh token from vault");

                    return Result.Fail("Failed to obtain session items from vault");
                }
                return Result.Ok((refreshToken, sharedSecret));
            }
            catch (Exception)
            {
                _logger.LogInformation("Failed to obtain token from vault");
                return Result.Fail("Failed to obtain session items from vault");
            }
        }

        public static async Task<Result> DeleteVaultItems(
            string SubscriptionID,
            string kvUrl,
            ILogger _logger,
            string clientId,
            string clientSecret
        )
        {
            string refreshTokenName = getRefreshTokenName(SubscriptionID);
            string secretTokenName = getSharedSecretName(SubscriptionID);

            _logger.LogInformation("Processing a request with refresh" + refreshTokenName);
            _logger.LogInformation("Processing a request with secret_token" + secretTokenName);

            var clientRes = GetSecretClient(kvUrl, clientId, clientSecret);
            if (clientRes.IsFailed)
            {
                return Result.Fail("Can't access vault");
            }

            SecretClient secretClient = clientRes.Value;

            DeleteSecretOperation? refreshToken = await secretClient.StartDeleteSecretAsync(
                refreshTokenName
            );
            DeleteSecretOperation? sharedSecret = await secretClient.StartDeleteSecretAsync(
                secretTokenName
            );

            return Result.Ok();
        }

        public static async Task<Result> DeleteOldVaultItems(
            string SubscriptionID,
            string kvUrl,
            ILogger _logger,
            string clientId,
            string clientSecret
        )
        {
            string refreshTokenName = "mail-" + getRefreshTokenName(SubscriptionID);
            string secretTokenName = "mail-" + getSharedSecretName(SubscriptionID);

            _logger.LogInformation("Processing a request with refresh" + refreshTokenName);
            _logger.LogInformation("Processing a request with secret_token" + secretTokenName);

            var clientRes = GetSecretClient(kvUrl, clientId, clientSecret);
            if (clientRes.IsFailed)
            {
                return Result.Fail("Can't access vault");
            }

            SecretClient secretClient = clientRes.Value;

            DeleteSecretOperation? refreshToken = await secretClient.StartDeleteSecretAsync(
                refreshTokenName
            );
            DeleteSecretOperation? sharedSecret = await secretClient.StartDeleteSecretAsync(
                secretTokenName
            );

            return Result.Ok();
        }

        public static async Task<Result<TokenResponse>> GetTokenFromCode(
            ILogger _logger,
            string refreshToken,
            string? clientId,
            string? clientSecret,
            string? scope
        )
        {
            _logger.LogInformation("Using token to get access token");

            Dictionary<string, string> dict = new Dictionary<string, string>();
            dict.Add("client_id", clientId ?? "");
            dict.Add("scope", scope ?? "");
            dict.Add("refresh_token", refreshToken);
            dict.Add("grant_type", "refresh_token");
            dict.Add("client_secret", clientSecret ?? "");

            HttpClient client = new HttpClient();
            HttpRequestMessage refreshReq = new HttpRequestMessage(
                HttpMethod.Post,
                "https://login.microsoftonline.com/common/oauth2/v2.0/token"
            )
            {
                Content = new FormUrlEncodedContent(dict),
            };
            HttpResponseMessage? refreshRes = await client.SendAsync(refreshReq);
            string tokenJson = await refreshRes.Content.ReadAsStringAsync();

            if (!refreshRes.IsSuccessStatusCode)
            {
                _logger.LogInformation("Failed to use refresh token to get access token");
                _logger.LogInformation(tokenJson);
                return Result.Fail(
                    new Error("Failed to use refresh token to get access token").CausedBy(tokenJson)
                );
            }

            TokenResponse? tr = JsonConvert.DeserializeObject<TokenResponse>(tokenJson);

            if (tr == null)
            {
                return Result.Fail("Failed to decode token response");
            }

            return Result.Ok(tr);
        }

        public static string? GetClientSecret(
            ILogger _logger,
            IConfiguration configuration,
            string? clientId
        )
        {
            if (string.IsNullOrEmpty(clientId))
            {
                return null;
            }

            string? clientSecret = configuration[$"{clientId}_apiClientSecret"];
            if (string.IsNullOrEmpty(clientSecret))
            {
                _logger.LogInformation("GetClientSecret:No client secret found for " + clientId);
                return null;
            }

            return clientSecret;
        }

        public static string? GetScope(
            ILogger _logger,
            IConfiguration configuration,
            string? clientId
        )
        {
            if (string.IsNullOrEmpty(clientId))
            {
                return null;
            }

            string? scope = configuration[$"{clientId}_apiScope"];
            if (string.IsNullOrEmpty(scope))
            {
                _logger.LogInformation("GetScope:No scope found for " + clientId);
                return null;
            }

            return scope;
        }
    }
}
