using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Policy;
using System.Threading.Tasks;
using AtveroEmailFilingAzure.src.Models.ApiResponses;
using FluentResults;

namespace AtveroEmailFilingAzure.src.Services.SharepointApi;

public interface ISharepointApi
{
    public Task<Result<SharePointSite>> GetSite(string siteUrl);
    public Task<Result<List<Hubsite>>> GetHubsites();
    public Task<Result<List<Hubsite>>> GetAtveroMailHubsites();
}
