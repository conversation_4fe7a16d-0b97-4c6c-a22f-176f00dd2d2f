using System.Text;
using AtveroEmailFilingAzure.src.Models.ApiResponses;
using FluentResults;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFilingAzure.src.Services.SharepointApi;

public class SharepointApi : ISharepointApi
{
    private readonly ILogger _logger;
    HttpClient _client;
    private string _sharepointTenant;

    public SharepointApi(string sharepointTenant, HttpClient httpClient, ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _client = httpClient;
        _sharepointTenant = sharepointTenant;
    }

    public async Task<Result<List<Hubsite>>> GetAtveroMailHubsites()
    {
        Result<List<Hubsite>> hubsites = await GetHubsites();

        if (hubsites.IsFailed)
        {
            _logger.LogInformation("Failed to fetch hubsites");
            return hubsites;
        }

        List<Hubsite> atveroMailHubsites = new();
        _logger.LogInformation("Discovered " + hubsites.Value.Count + " hubsites");

        foreach (Hubsite hubsite in hubsites.Value)
        {
            if (!string.IsNullOrEmpty(hubsite.SiteUrl))
            {
                Result<bool> isAtveroMailCapable = await IsAtveroMailCapable(hubsite.SiteUrl);
                if (isAtveroMailCapable.ValueOrDefault)
                {
                    atveroMailHubsites.Add(hubsite);
                }
            }
            else
            {
                _logger.LogInformation("Can't check empty hubsite information");
            }
        }

        _logger.LogInformation("Discovered " + atveroMailHubsites.Count + " Mail Enabled hubsites");

        return Result.Ok(atveroMailHubsites);
    }

    private async Task<Result<bool>> IsAtveroMailCapable(string siteUrl)
    {
        _logger.LogInformation("Checking if is AtveroMailCapable for " + siteUrl);
        HttpResponseMessage response = await _client.GetAsync(
            $"{siteUrl}/_api/web/lists/GetByTitle('Filed Email Content')"
        );
        string content = await response.Content.ReadAsStringAsync();

        if (response.IsSuccessStatusCode)
        {
            _logger.LogInformation("Filed Email Content was found");
            return Result.Ok(true);
        }
        else
        {
            _logger.LogInformation("Filed Email Content was not found");
            _logger.LogInformation(response.StatusCode.ToString());

            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return Result.Ok(false);
            }
            else
            {
                return Result.Fail(
                    new Error(
                        $"Failed to check if site is Atvero Mail capable. Status code: {response.StatusCode}"
                    ).CausedBy(content)
                );
            }
        }
    }

    public async Task<Result<List<Hubsite>>> GetHubsites()
    {
        Result<List<Hubsite>> result;

        try
        {
            HttpResponseMessage response = await _client.GetAsync(
                $"{_sharepointTenant}/_api/hubsites"
            );
            string content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                HubsiteResponse? hubsites =
                    System.Text.Json.JsonSerializer.Deserialize<HubsiteResponse>(content);
                if (null == hubsites?.value)
                {
                    return Result.Fail<List<Hubsite>>("Failed to deserialize hubsites.");
                }
                else
                {
                    result = Result.Ok(hubsites.value);
                }
            }
            else
            {
                result = Result.Fail<List<Hubsite>>(
                    new Error(
                        $"Failed to get hubsites. Status code: {response.StatusCode}"
                    ).CausedBy(content)
                );
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get hubsites.");
            result = Result.Fail<List<Hubsite>>(new Error("Failed to get hubsites.").CausedBy(ex));
        }

        return result;
    }

    public async Task<Result<SharePointSite>> GetSite(string siteUrl)
    {
        try
        {
            using (
                HttpRequestMessage requestMessage = new HttpRequestMessage(
                    HttpMethod.Get,
                    $"{siteUrl}/_api/web"
                )
            )
            {
                requestMessage.Headers.Add("Accept", "application/json");

                HttpResponseMessage response = await _client.SendAsync(requestMessage);
                string content = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    SharePointSite? site =
                        System.Text.Json.JsonSerializer.Deserialize<SharePointSite>(content);
                    if (null == site)
                    {
                        return Result.Fail<SharePointSite>("Failed to deserialize site.");
                    }
                    else
                    {
                        return Result.Ok(site);
                    }
                }
                else
                {
                    return Result.Fail(
                        new Error("Unable to get site " + siteUrl).CausedBy(content)
                    );
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get hubsites.");
            return Result.Fail(new Error("Failed to get hubsites.").CausedBy(ex));
        }
    }

    public async Task<Result> JoinHubsite(string sitePath, string hubsiteId)
    {
        try
        {
            using (
                HttpRequestMessage requestMessage = new HttpRequestMessage(
                    HttpMethod.Post,
                    $"{sitePath}/_api/site/JoinHubSite('{hubsiteId}')"
                )
            )
            {
                HttpResponseMessage response = await _client.SendAsync(requestMessage);
                string content = await response.Content.ReadAsStringAsync();

                _logger.LogInformation(content);

                if (response.IsSuccessStatusCode)
                {
                    return Result.Ok();
                }
                else
                {
                    return Result.Fail(new Error("Unable to join hubsite").CausedBy(content));
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to join hubsite.");
            return Result.Fail(new Error("Failed to join hubsite.").CausedBy(ex));
        }
    }

    public async Task<Result<CreateSiteResponse>> CreateSite(
        string hubsiteUrl,
        string siteName,
        string? description,
        string? hubsiteid
    )
    {
        try
        {
            Uri hubsiteUri = new Uri(hubsiteUrl);
            string host = hubsiteUri.Host;

            var tenanturl = hubsiteUri.Scheme + "://" + hubsiteUri.Host;

            string siteUrl = tenanturl + "/sites/" + siteName;

            string displayName = description != null ? siteName + " - " + description : siteName;

            CreateSiteFields csRequest = new CreateSiteFields
            {
                Title = displayName,
                WebTemplate = "STS#3", // not joined to a group
                Url = siteUrl,
                ShareByEmailEnabled = false,
            };

            CreateSiteRequest request = new CreateSiteRequest { request = csRequest };

            if (!string.IsNullOrEmpty(description))
            {
                request.request.Description = description;
            }

            var jsonString = JsonConvert.SerializeObject(request);

            using (
                HttpRequestMessage requestMessage = new HttpRequestMessage(
                    HttpMethod.Post,
                    $"{tenanturl}/_api/SPSiteManager/create"
                )
            )
            {
                requestMessage.Content = new System.Net.Http.StringContent(
                    jsonString,
                    Encoding.UTF8,
                    "application/json"
                );
                requestMessage.Headers.Add("Accept", "application/json;odata.metadata=none");
                requestMessage.Headers.Add("odata-version", "4.0");

                HttpResponseMessage response = await _client.SendAsync(requestMessage);
                string content = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    CreateSiteResponse? site =
                        System.Text.Json.JsonSerializer.Deserialize<CreateSiteResponse>(content);
                    if (null == site)
                    {
                        return Result.Fail<CreateSiteResponse>("Failed to deserialize site.");
                    }
                    else
                    {
                        // if we have a hubsite ID, associate it

                        if (!string.IsNullOrEmpty(hubsiteid) && !string.IsNullOrEmpty(site.SiteId))
                        {
                            await JoinHubsite(siteUrl, hubsiteid);
                        }

                        return Result.Ok(site);
                    }
                }
                else
                {
                    _logger.LogInformation("Unable to create site " + content);

                    _logger.LogError("Unable to create site");
                    return Result.Fail(
                        new Error("Unable to create site " + siteName).CausedBy(content)
                    );
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create site.");
            return Result.Fail(new Error("Failed to create site.").CausedBy(ex));
        }
    }

    class HubsiteResponse
    {
        public List<Hubsite>? value { get; set; }
    }

    class CreateSiteFields
    {
        public required string Title { get; set; }
        public string? Url { get; set; }
        public bool? ShareByEmailEnabled { get; set; }
        public string? Description { get; set; }
        public string? WebTemplate { get; set; }

        //   public string? SiteDesignId { get; set; } // add this back if you want to set a site design
    }

    class CreateSiteRequest
    {
        public required CreateSiteFields request { get; set; }
    }

    public class CreateSiteResponse
    {
        public string? SiteId { get; set; }
        public int SiteStatus { get; set; }
        public string? SiteUrl { get; set; }
    }
}
