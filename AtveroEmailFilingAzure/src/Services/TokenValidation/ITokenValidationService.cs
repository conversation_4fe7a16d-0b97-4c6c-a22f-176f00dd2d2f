﻿using AtveroEmailFiling.Models;
using FluentResults;
using Microsoft.Azure.Functions.Worker.Http;

namespace AtveroEmailFiling.Services.TokenValidation
{
    public interface ITokenValidationService
    {
        public Task<string?> ValidateAuthorizationHeaderAsync(HttpRequestData request);
        public Task<string?> ValidateCommonAuthorizationHeaderAsync(HttpRequestData request);
        bool IsTokenExpired(string token);
        DateTime? GetTokenExpiryDateTime(string token);
        TokenDetails GetUserDetails(string token);
    }
}
