namespace AtveroEmailFilingAzure.Services;

using AtveroEmailFiling.Models;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.EmailMetadata;
using AtveroEmailFiling.Services.EmailUploading;
using AtveroEmailFiling.Services.FilingStatusCheck;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.GraphApiService.Drives;
using AtveroEmailFiling.Services.GraphApiService.Emails;
using AtveroEmailFiling.Utils;
using FluentResults;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;

public class ProcessEmailFiling
{
    private readonly ILogger _logger;
    private readonly IAzureTableService _tableService;
    private readonly IGraphDriveService _graphDriveService;
    private readonly IGraphApiClient _graphApiClient;
    private readonly IFilingStatusCheckService _filingStatusCheckService;
    private readonly IEmailUploadingService _emailUploadingService;
    private readonly IEmailMetadataService _emailMetadataService;

    private readonly IGraphEmailService _graphEmailService;

    private readonly IConfiguration _config;

    private async Task<Result> ProcessMessage(
        Message message,
        GraphServiceClient graphClient,
        EmailRetrievalService emailRetrievalService,
        IGraphEmailService graphEmailService,
        string userName,
        string customerDomain,
        string filedBy,
        string activeUser,
        string? movetoFiledFolder
    )
    {
        string error = "No Error";

        string? driveId = null;

        try
        {
            if (message.SitePath != null)
            {
                _logger.LogInformation(
                    "Using site path from message to get drive ID " + message.SitePath
                );
                Result<string> driveIdRes = await _graphDriveService.GetDriveIdFromSitePathAsync(
                    message.SitePath,
                    message.Confidential,
                    graphClient
                );

                if (driveIdRes.IsSuccess)
                {
                    driveId = driveIdRes.Value;
                }
                else
                {
                    _logger.LogInformation(
                        "Failed to get drive ID from site path " + message.SitePath
                    );
                }
            }
            else
            {
                // fall back to root site, can't process multi-geo

                _logger.LogInformation("Using project code to get drive ID " + message.ProjectCode);

                Result<string> driveIdRes = await _graphDriveService.GetDriveIdFromProjectCodeAsync(
                    message.ProjectCode,
                    message.Confidential,
                    graphClient
                );

                if (driveIdRes.IsSuccess)
                {
                    driveId = driveIdRes.Value;
                }
                else
                {
                    _logger.LogInformation(
                        "Failed to get drive ID from project code " + message.ProjectCode
                    );
                }
            }

            if (driveId == null)
            {
                // can't get drive
                _logger.LogInformation($"Unable to locate project drive for {message.ProjectCode}");
            }
            else
            {
                // map from persistent ID

                string messageId = message.MessageId; // Fall back to the original

                if (message.PersistentMessageId != null)
                {
                    _logger.LogInformation("We have a persistent ID, mapping it");

                    List<string> InputIds = new List<string>();
                    InputIds.Add(message.PersistentMessageId);

                    var res = await graphEmailService.MapFromPersistentIDs(
                        InputIds,
                        graphClient,
                        activeUser
                    );
                    if (res.IsSuccess)
                    {
                        // only one result

                        if (res.Value.ContainsKey(message.PersistentMessageId))
                        {
                            _logger.LogInformation("Mapped successfully");

                            messageId = res.Value[message.PersistentMessageId];
                        }
                        else
                        {
                            _logger.LogInformation("Persistent ID not found in mapped values");
                        }
                    }
                    else
                    {
                        _logger.LogInformation("Failed to  call mapping");
                    }
                }

                Result<Microsoft.Graph.Models.Message> msgResult =
                    await _graphApiClient.GetEmailByIdAsync(messageId, graphClient, activeUser);

                if (msgResult.IsSuccess && msgResult.Value.ConversationId != null)
                {
                    Microsoft.Graph.Models.Message msg = msgResult.Value;

                    // could optimise this to see if we've already processes this conversation this run
                    // for when users select multiple emails in the same conversation to file

                    _logger.LogInformation(
                        "Getting emails in conversation "
                            + msg.ConversationId
                            + " for "
                            + activeUser
                    );

                    Result<List<Microsoft.Graph.Models.Message>> emailsInConversationResult =
                        await emailRetrievalService.GetEmailsInConversationAsync(
                            msg.ConversationId,
                            graphClient,
                            activeUser
                        );

                    List<Microsoft.Graph.Models.Message> emailsInConversation;

                    if (emailsInConversationResult.IsSuccess)
                    {
                        emailsInConversation = emailsInConversationResult.Value;
                    }
                    else
                    {
                        _logger.LogInformation(error);
                        if (emailsInConversationResult.Errors.Count > 0)
                        {
                            _logger.LogInformation(
                                emailsInConversationResult.Errors.First().ToString()
                            );
                        }

                        _logger.LogError("Failed to get messages in conversation");

                        _logger.LogInformation("Falling back to filing just this message");

                        emailsInConversation = [msg];
                    }

                    List<(
                        OutlookMailMessage msg,
                        string filingPath,
                        string messageId
                    )> messagesToFile =
                        new List<(OutlookMailMessage msg, string filingPath, string messageId)>();

                    foreach (Microsoft.Graph.Models.Message email in emailsInConversation)
                    {
                        // we want to use the immutableId by preference if we can

                        string? emailMessageId = email.Id;
                        try
                        {
                            var res = await _graphEmailService.MapFromRestIdToPersistentIDs(
                                [emailMessageId],
                                graphClient,
                                activeUser
                            );
                            if (res.IsSuccess)
                            {
                                Dictionary<string, string> maps = res.Value;

                                if (maps.Keys.Count > 0)
                                {
                                    string key = maps.Keys.First();
                                    emailMessageId = res.Value[key];

                                    _logger.LogDebug(
                                        "Mapped conversation email to persistent ID"
                                            + emailMessageId
                                    );
                                }
                            }
                            else
                            {
                                _logger.LogInformation(
                                    "Unable to convert conversation message ID to persistent value"
                                );
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Failed to map a message in a conversation to a persistent ID"
                            );
                        }

                        if (emailMessageId != null)
                        {
                            var emailToFile = EmailUtils.CreateOutlookMailMessage(email);

                            _logger.LogInformation("Email subject is " + emailToFile.Subject);

                            string filingPath = EmailUtils.GenerateFilingPathCheckSubject(
                                emailToFile,
                                message.Tag
                            );

                            bool isEmailAlreadySaved =
                                await _filingStatusCheckService.IsEmailAlreadySavedAsync(
                                    filingPath,
                                    driveId,
                                    graphClient
                                );

                            if (isEmailAlreadySaved)
                            {
                                _logger.LogInformation("Email is already filed");
                                await _emailMetadataService.SetEmailMetadata(
                                    emailMessageId,
                                    message.ProjectCode,
                                    graphClient,
                                    message.Tag,
                                    message.Important,
                                    message.Confidential,
                                    AtveroEmailFilingAzure.Constants.FILED_TAG,
                                    userName,
                                    activeUser
                                );

                                if (movetoFiledFolder != null)
                                {
                                    await EmailUtils.MoveFiledEmailToFolder(
                                        graphClient,
                                        activeUser,
                                        emailMessageId,
                                        movetoFiledFolder,
                                        _logger
                                    );
                                }
                            }
                            else
                            {
                                messagesToFile.Add((emailToFile, filingPath, emailMessageId));
                            }
                        }
                    }

                    MetaData metadata = new MetaData()
                    {
                        Confidential = message.Confidential,
                        Important = message.Important,
                        Tag = message.Tag,
                        ProjectCode = message.ProjectCode,
                        SitePath = message.SitePath,
                    };

                    Result uploadedAll = await _emailUploadingService.ProcessMessagesAsync(
                        messagesToFile,
                        driveId,
                        graphClient,
                        metadata,
                        _emailMetadataService,
                        filedBy,
                        customerDomain,
                        AtveroEmailFilingAzure.Constants.FILED_TAG,
                        activeUser,
                        userName,
                        movetoFiledFolder
                    );

                    try
                    {
                        Conversation newConversation = new Conversation()
                        {
                            PartitionKey = activeUser,
                            RowKey = msg.ConversationId,
                            ProjectCode = message.ProjectCode,
                            Tag = message.Tag,
                            Confidential = message.Confidential,
                            Important = message.Important,
                            SitePath = message.SitePath,
                        };

                        await _filingStatusCheckService.UpsertConversationAsync(
                            newConversation,
                            customerDomain
                        );
                    }
                    catch (Exception)
                    {
                        _logger.LogInformation(
                            "Can't add filed conversation to table, but continuing"
                        );
                    }

                    if (uploadedAll.IsSuccess)
                    {
                        message.Status = JobStatus.Completed;
                        await _tableService.UpdateEntityAsync<Message>(
                            "QueuedMessage",
                            message,
                            customerDomain
                        );
                        return Result.Ok();
                    }
                    else
                    {
                        error = "Failed up upload all messages in the conversation";
                        _logger.LogInformation(error);
                    }
                }
                else
                {
                    error = "Failed accessing original message from Exchange";
                    _logger.LogError(error);
                }
            }
        }
        catch (Exception ex)
        {
            SentrySdk.CaptureException(ex);
            error = "Exception: " + ex.Message;
        }

        if (message.DequeueCount > 4) // try up to 5 times
        {
            // give up
            message.Status = JobStatus.Failed;
            message.FailureReason = error;
            await _tableService.UpdateEntityAsync<Message>(
                "QueuedMessage",
                message,
                customerDomain
            );
        }
        else
        {
            message.DequeueCount += 1;
            await _tableService.UpdateEntityAsync<Message>(
                "QueuedMessage",
                message,
                customerDomain
            );
        }

        return Result.Fail(error);
    }

    public ProcessEmailFiling(
        ILogger logger,
        IAzureTableService tableService,
        IGraphDriveService graphDriveService,
        IGraphApiClient graphApiClient,
        IFilingStatusCheckService filingStatusCheckService,
        IEmailUploadingService emailUploadingService,
        IEmailMetadataService emailMetadataService,
        IGraphEmailService graphEmailService,
        IConfiguration config
    )
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _tableService = tableService;
        _graphDriveService = graphDriveService;
        _graphApiClient = graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
        _filingStatusCheckService = filingStatusCheckService;
        _emailUploadingService = emailUploadingService;
        _emailMetadataService = emailMetadataService;
        _graphEmailService = graphEmailService;
        _config = config;
    }

    public async Task<bool> ProcessQueuedMessages(
        string userName,
        string customerDomainRaw,
        GraphServiceClient graphClient,
        string activeUser
    )
    {
        // keep processing for 25 minutes
        // then stop so we release locks nicely

        var timeoutTime = DateTime.UtcNow.Add(TimeSpan.FromMinutes(25));

        string customerDomain = customerDomainRaw;

        string? mappedDomain = await DomainUtils.MapDomain(customerDomainRaw, _tableService);
        if (mappedDomain != null)
        {
            _logger.LogInformation($"Changing to parent domain {mappedDomain}");
            customerDomain = mappedDomain;
        }

        string? movetoFiledFolder = await AppSettings.GetSetting(
            _tableService,
            customerDomain,
            "MoveToFiledFolder"
        );

        string escapedEmail = activeUser.Replace("'", "''");

        string filter = $"PartitionKey eq '{escapedEmail}' and Status eq 'Pending'";

        List<Message> messages = await _tableService.QueryEntitiesAsync<Message>(
            "QueuedMessage",
            filter,
            customerDomain
        );

        string? filedBy =
            await _graphApiClient.GetUserDisplayNameAsync(graphClient) ?? "unknown user";

        while (messages?.Count > 0 && DateTime.UtcNow < timeoutTime)
        {
            // we have some messages to process
            _logger.LogInformation("We have " + messages.Count + " emails to process");

            EmailRetrievalService emailRetrievalService = new EmailRetrievalService(
                _graphApiClient,
                _logger
            );

            foreach (Message message in messages)
            {
                Result res = await ProcessMessage(
                    message,
                    graphClient,
                    emailRetrievalService,
                    _graphEmailService,
                    userName,
                    customerDomain,
                    filedBy,
                    activeUser,
                    movetoFiledFolder
                );

                if (res.IsFailed)
                {
                    string? error = res.Errors.First().ToString();

                    _logger.LogInformation(error);

                    string? alertUrl = _config["TeamsAlertURL"];
                    if (alertUrl != null)
                    {
                        await AlertUtils.SendTeamsAlert(
                            alertUrl,
                            "Atvero-Mail: Failed to file email for " + activeUser,
                            error
                        );
                    }
                }
                else
                {
                    _logger.LogInformation("Message processed successfully");
                    await StatsUtils.LogMessageActiveUserClick(
                        _tableService,
                        customerDomain,
                        userName,
                        _logger
                    );
                }
            }

            //  get any messages that need processing, and keep going until out timeout threshold is hit
            messages = await _tableService.QueryEntitiesAsync<Message>(
                "QueuedMessage",
                filter,
                customerDomain
            );

            _logger.LogInformation(
                $"Found {messages?.Count} messages added to the queue whilst we held the lock "
            );
        }

        return true;
    }
}
