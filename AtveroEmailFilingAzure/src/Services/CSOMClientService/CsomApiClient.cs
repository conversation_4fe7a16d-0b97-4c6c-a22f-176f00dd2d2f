﻿using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFilingAzure.src.Models.ApiResponses;
using FluentResults;
using Microsoft.Office.SharePoint.Tools;
using Microsoft.Online.SharePoint.TenantAdministration;
using Microsoft.SharePoint.Client;

namespace AtveroEmailFiling.Services.CsomClientService
{
    public class CsomApiClient : ICsomApiClient
    {
        public CsomApiClient() { }

        public async Task<Result<bool>> CanWriteToList(string listName, ClientContext context)
        {
            var web = context.Web;
            var user = web.CurrentUser;

            context.Load(user, u => u.LoginName);
            List list = web.Lists.GetByTitle(listName);
            context.Load(list, l => l.EffectiveBasePermissions);
            try
            {
                // This will fail if the does not have any access to the site
                await context.ExecuteQueryAsync();

                //check edit rights on the object
                return Result.Ok(list.EffectiveBasePermissions.Has(PermissionKind.AddListItems));
            }
            catch (ServerUnauthorizedAccessException)
            {
                return Result.Ok(false);
            }
            catch (Exception e)
            {
                return Result.Fail<bool>(
                    new Error("Problem accessing the site to check permissions").CausedBy(e)
                );
            }
        }

        public async Task<Result<List<Project>>> GetProjectsAsync(
            string? search,
            string hubsitePath,
            ClientContext context
        )
        {
            var web = context.Web;
            List list = web.Lists.GetByTitle("Projects");

            string whereClause = "";
            if (!string.IsNullOrEmpty(search))
            {
                whereClause = $$"""
                      <Or>
                        <Contains>
                          <FieldRef Name='ProjectCode'/>
                            <Value Type='Text'>{{search}}</Value>
                        </Contains>
                        <Contains>
                          <FieldRef Name='Description'/><Value Type='Text'>{{search}}</Value>
                        </Contains>
                      </Or>
                    """;
            }

            CamlQuery camlQuery = new()
            {
                ViewXml = $$"""
                    <View Scope="RecursiveAll">
                          <Query>
                            <Where>{{whereClause}}</Where>
                            <OrderBy><FieldRef Name="ProjectCode" Ascending="True"/></OrderBy>
                          </Query>
                          <ViewFields>
                            <FieldRef Name="ProjectCode"/>
                            <FieldRef Name="Description"/>
                            <FieldRef Name="ATVImportedSourceID"/>
                          </ViewFields>
                          <RowLimit Paged="TRUE">200</RowLimit>
                        </View>
                    """,
            };
            try
            {
                ListItemCollection listItemCollection = list.GetItems(camlQuery);
                context.Load(listItemCollection);
                await context.ExecuteQueryAsync();

                List<Project> projects = [];

                foreach (var project in listItemCollection)
                {
                    string? projectCode = project["ProjectCode"]?.ToString();

                    if (projectCode != null)
                    {
                        string? title = project["Description"]?.ToString();

                        projects.Add(
                            new Project
                            {
                                Id = project.Id.ToString(),
                                ProjectCode = projectCode,
                                ProjectTitle = title ?? projectCode,
                            }
                        );
                    }
                }

                return Result.Ok(projects);
            }
            catch (Exception e)
            {
                return Result.Fail(new Error("Error getting projects").CausedBy(e));
            }
        }

        public async Task<Result> GrantHubsiteRights(
            string sitePath,
            string users,
            ClientContext csomClientContext
        )
        {
            if (string.IsNullOrEmpty(sitePath))
            {
                return Result.Fail("Site path is required");
            }

            if (string.IsNullOrEmpty(users))
            {
                return Result.Fail("Users are required");
            }
            try
            {
                string[] usersArray = users.Split(',');

                var tenant = new Tenant(csomClientContext);

                Uri hubSiteUrl = new Uri(
                    new Uri(csomClientContext.Url.Replace("-admin", "")),
                    sitePath
                );

                tenant.GrantHubSiteRights(
                    hubSiteUrl.ToString(),
                    usersArray,
                    SPOHubSiteUserRights.Join
                );

                await csomClientContext.ExecuteQueryAsync();
                return Result.Ok();
            }
            catch (Exception e)
            {
                return Result.Fail(new Error("Error granting hubsite rights").CausedBy(e));
            }
        }

        public async Task<Result<List<HubsiteUser>>> GetHubsiteRights(
            string sitePath,
            ClientContext csomClientContext
        )
        {
            if (string.IsNullOrEmpty(sitePath))
            {
                return Result.Fail("Site path is required");
            }

            try
            {
                var tenant = new Tenant(csomClientContext);

                Uri hubSiteUrl = new Uri(
                    new Uri(csomClientContext.Url.Replace("-admin", "")),
                    sitePath
                );

                // Get hub site rights for the specified site
                HubSiteProperties hubsiteProperties = tenant.GetHubSitePropertiesByUrl(
                    hubSiteUrl.ToString()
                );
                csomClientContext.Load(hubsiteProperties);
                csomClientContext.Load(hubsiteProperties, h => h.Permissions);
                await csomClientContext.ExecuteQueryAsync();

                List<HubsiteUser> userList = new List<HubsiteUser>();

                // Extract user login names from the hub site rights
                if (hubsiteProperties.Permissions != null)
                {
                    foreach (HubSitePermission permission in hubsiteProperties.Permissions)
                    {
                        if (SPOHubSiteUserRights.Join == permission.Rights)
                        {
                            if (!string.IsNullOrEmpty(permission.PrincipalName))
                            {
                                userList.Add(
                                    new()
                                    {
                                        Id = permission.PrincipalName,
                                        Name = permission.DisplayName,
                                    }
                                );
                            }
                        }
                    }
                }

                return Result.Ok(userList);
            }
            catch (Exception e)
            {
                return Result.Fail(new Error("Error getting hubsite rights").CausedBy(e));
            }
        }

        public async Task<Result> ImportSearchSchemaAsync(
            string schema,
            ClientContext csomClientContext
        )
        {
            try
            {
                var Search =
                    new Microsoft.SharePoint.Client.Search.Portability.SearchConfigurationPortability(
                        csomClientContext
                    );
                //SPSiteSubscription for tenant level
                var Owner = new Microsoft.SharePoint.Client.Search.Administration.SearchObjectOwner(
                    csomClientContext,
                    Microsoft
                        .SharePoint
                        .Client
                        .Search
                        .Administration
                        .SearchObjectLevel
                        .SPSiteSubscription
                );

                // import search conf schema
                Search.ImportSearchConfiguration(Owner, schema);
                await csomClientContext.ExecuteQueryAsync();
                return Result.Ok();
            }
            catch (Exception e)
            {
                return Result.Fail(new Error("Error importing search schema").CausedBy(e));
            }
        }
    }
}
