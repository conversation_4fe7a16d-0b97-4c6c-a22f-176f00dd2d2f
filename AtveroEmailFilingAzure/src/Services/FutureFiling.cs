namespace AtveroEmailFilingAzure.Services;

using AtveroEmailFiling.Models;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.EmailMetadata;
using AtveroEmailFiling.Services.EmailUploading;
using AtveroEmailFiling.Services.FilingStatusCheck;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.GraphApiService.Drives;
using AtveroEmailFiling.Services.GraphApiService.Emails;
using AtveroEmailFiling.Utils;
using FluentResults;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;

public class FutureFiling
{
    private readonly ILogger _logger;
    private readonly IAzureTableService _tableService;
    private readonly IGraphDriveService _graphDriveService;
    private readonly IGraphApiClient _graphApiClient;
    private readonly IFilingStatusCheckService _filingStatusCheckService;
    private readonly IEmailUploadingService _emailUploadingService;
    private readonly IEmailMetadataService _emailMetadataService;

    private readonly IGraphEmailService _graphEmailService;

    private readonly ISyncTableService _syncService;

    public FutureFiling(
        ILogger logger,
        IAzureTableService tableService,
        IGraphDriveService graphDriveService,
        IGraphApiClient graphApiClient,
        IFilingStatusCheckService filingStatusCheckService,
        IEmailUploadingService emailUploadingService,
        IEmailMetadataService emailMetadataService,
        IGraphEmailService graphEmailService,
        ISyncTableService syncService
    )
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _tableService = tableService;
        _graphDriveService = graphDriveService;
        _graphApiClient = graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
        _filingStatusCheckService = filingStatusCheckService;
        _emailUploadingService = emailUploadingService;
        _emailMetadataService = emailMetadataService;
        _graphEmailService =
            graphEmailService ?? throw new ArgumentNullException(nameof(graphEmailService));
        _syncService = syncService ?? throw new ArgumentNullException(nameof(syncService));
    }

    public async Task<bool> ProcessFutureFiling(
        string userName,
        string customerDomainRaw,
        GraphServiceClient graphClient,
        DateTime? tokenExpiry,
        string activeUser
    )
    {
        // keep processing for 25 minutes
        // then stop so we release locks nicely

        var timeoutTime = DateTime.UtcNow.Add(TimeSpan.FromMinutes(25));

        string customerDomain = customerDomainRaw;

        string? mappedDomain = await DomainUtils.MapDomain(customerDomainRaw, _tableService);
        if (mappedDomain != null)
        {
            _logger.LogInformation($"Changing to parent domain {mappedDomain}");
            customerDomain = mappedDomain;
        }

        Result<List<Microsoft.Graph.Models.MailFolder>> filingFoldersRes =
            await _graphEmailService.GetFilingFolders(graphClient, activeUser);

        if (filingFoldersRes.IsFailed)
        {
            _logger.LogInformation("Failed loading email folders");
            return false;
        }

        List<Microsoft.Graph.Models.MailFolder> userFolders = filingFoldersRes.Value;

        foreach (Microsoft.Graph.Models.MailFolder folder in userFolders)
        {
            _logger.LogInformation("Processing mail folder " + folder.DisplayName);

            if (folder.DisplayName == "Drafts")
            {
                continue;
            }

            if (tokenExpiry <= DateTime.UtcNow)
            {
                _logger.LogWarning("Token expired before processing. ");
                return false;
            }

            // get a list of all messages received since the last run

            if (folder.Id != null)
            {
                // we always tag items in Sent Items as Filed by the user

                string filingTag = AtveroEmailFilingAzure.Constants.AUTOFILED_TAG;

                if (folder.DisplayName == AtveroEmailFilingAzure.Constants.SENT_ITEMS)
                {
                    filingTag = AtveroEmailFilingAzure.Constants.FILED_TAG;
                }

                //var lastSyncTime = _syncService.GetLastRetrievalTimeAsync(userName, customerDomain);
                Result<(List<Microsoft.Graph.Models.Message>, FilingFolder?)> receivedEmailsResult =
                    await _graphEmailService.GetEmailByDelta(
                        graphClient,
                        folder.Id,
                        folder.DisplayName ?? "No folder name",
                        userName,
                        customerDomain,
                        folder.Id,
                        activeUser
                    );

                if (receivedEmailsResult.IsSuccess)
                {
                    _logger.LogInformation("Processing a set of emails found by delta");
                    List<Microsoft.Graph.Models.Message> receivedEmails = receivedEmailsResult
                        .Value
                        .Item1;

                    FilingFolder? filingFolderSync = receivedEmailsResult.Value.Item2;

                    _logger.LogInformation(
                        "We have " + receivedEmails.Count + " received emails to process"
                    );

                    foreach (Microsoft.Graph.Models.Message email in receivedEmails.Take(1000)) // just in case we do something bad!
                    {
                        if (DateTime.UtcNow >= timeoutTime)
                        {
                            // run out of time
                            break;
                        }

                        try
                        {
                            if (email.ConversationId == null)
                            {
                                _logger.LogInformation("Skipping message with no conversation id");
                                continue;
                            }

                            if (EmailUtils.AlreadyFiledCategories(email.Categories))
                            {
                                // _logger.LogInformation(
                                //     "Skipping message as already marked as filed"
                                // );
                                continue;
                            }

                            _logger.LogInformation(
                                "Received email with conversation id" + email.ConversationId
                            );

                            string escapedEmail = activeUser.Replace("'", "''");

                            List<Conversation> filedConversations =
                                await _tableService.QueryEntitiesAsync<Conversation>(
                                    "Conversations",
                                    $"PartitionKey eq '{escapedEmail}' and RowKey eq '{email.ConversationId}'",
                                    customerDomain
                                );

                            _logger.LogInformation(
                                "We have "
                                    + filedConversations.Count
                                    + " filings for this conversation for user "
                                    + activeUser
                            );

                            if (filedConversations.Count > 0)
                            {
                                Conversation? conversationToApply = filedConversations.First();

                                _logger.LogInformation(
                                    "Update later email in conversation with metadata"
                                );

                                _logger.LogInformation(
                                    "Project code " + conversationToApply.ProjectCode
                                );

                                string? driveId = null;

                                if (conversationToApply.SitePath != null)
                                {
                                    _logger.LogInformation(
                                        "Using site path from message to get drive ID "
                                            + conversationToApply.SitePath
                                    );
                                    Result<string> driveIdRes =
                                        await _graphDriveService.GetDriveIdFromSitePathAsync(
                                            conversationToApply.SitePath,
                                            conversationToApply.Confidential,
                                            graphClient
                                        );

                                    if (driveIdRes.IsSuccess)
                                    {
                                        driveId = driveIdRes.Value;
                                    }
                                    else
                                    {
                                        _logger.LogInformation(
                                            "Failed to get drive ID from site path "
                                                + conversationToApply.SitePath
                                        );
                                    }
                                }
                                else
                                {
                                    // fall back to root site, can't process multi-geo
                                    _logger.LogInformation(
                                        "Using project code to get drive ID "
                                            + conversationToApply.ProjectCode
                                    );

                                    Result<string> driveIdRes =
                                        await _graphDriveService.GetDriveIdFromProjectCodeAsync(
                                            conversationToApply.ProjectCode,
                                            conversationToApply.Confidential,
                                            graphClient
                                        );

                                    if (driveIdRes.IsSuccess)
                                    {
                                        driveId = driveIdRes.Value;
                                    }
                                    else
                                    {
                                        _logger.LogInformation(
                                            "Failed to get drive ID from project code "
                                                + conversationToApply.ProjectCode
                                        );
                                    }
                                }

                                if (driveId == null)
                                {
                                    // can't get drive
                                    _logger.LogWarning(
                                        $"Unable to locate project drive for {conversationToApply.ProjectCode}"
                                    );
                                    throw new Exception(
                                        $"Unable to access destination project {conversationToApply.ProjectCode}"
                                    );
                                }

                                //_logger.LogInformation("Destination drive is" + driveId);

                                List<(
                                    OutlookMailMessage msg,
                                    string filingPath,
                                    string messageId
                                )> messagesToFile =
                                    new List<(
                                        OutlookMailMessage msg,
                                        string filingPath,
                                        string messageId
                                    )>();

                                if (email.Id != null)
                                {
                                    var emailToFile = EmailUtils.CreateOutlookMailMessage(email);

                                    _logger.LogInformation(
                                        "Email subject is " + emailToFile.Subject
                                    );

                                    string filingPath = EmailUtils.GenerateFilingPathCheckSubject(
                                        emailToFile,
                                        conversationToApply.Tag
                                    );

                                    // _logger.LogInformation("filing path is " + filingPath);

                                    bool isEmailAlreadySaved =
                                        await _filingStatusCheckService.IsEmailAlreadySavedAsync(
                                            filingPath,
                                            driveId,
                                            graphClient
                                        );

                                    if (isEmailAlreadySaved)
                                    {
                                        //_logger.LogInformation("Is already filed");
                                        await _emailMetadataService.SetEmailMetadata(
                                            email.Id,
                                            conversationToApply.ProjectCode,
                                            graphClient,
                                            conversationToApply.Tag,
                                            conversationToApply.Important,
                                            conversationToApply.Confidential,
                                            filingTag,
                                            userName,
                                            activeUser
                                        );
                                    }
                                    else
                                    {
                                        _logger.LogInformation(
                                            "Found an email to file to file to " + filingPath
                                        );
                                        messagesToFile.Add((emailToFile, filingPath, email.Id));
                                    }
                                }

                                //_logger.LogInformation("Uploading emails to SharePoint");

                                MetaData metadata = new MetaData()
                                {
                                    Confidential = conversationToApply.Confidential,
                                    Important = conversationToApply.Important,
                                    Tag = conversationToApply.Tag,
                                    ProjectCode = conversationToApply.ProjectCode,
                                };

                                Result uploadedAll =
                                    await _emailUploadingService.ProcessMessagesAsync(
                                        messagesToFile,
                                        driveId,
                                        graphClient,
                                        metadata,
                                        _emailMetadataService,
                                        userName,
                                        customerDomain,
                                        filingTag,
                                        activeUser,
                                        userName,
                                        null
                                    );
                            }
                            else
                            {
                                // _logger.LogInformation("Conversation hasn't already been filed.");
                            }
                        }
                        catch (Exception ex)
                        {
                            // this can happen when future filing runs before messages are filed on first run
                            _logger.LogInformation(
                                "Failed checking for previous filing of message " + ex.Message
                            );
                        }
                    }

                    // optimistically think we succeeeded - track in future

                    if (filingFolderSync != null)
                    {
                        //_logger.LogInformation("Updating the folder sync table");
                        await _tableService.UpsertEntityAsync(
                            "FilingFolders",
                            filingFolderSync,
                            customerDomain
                        );
                    }
                }
                else
                {
                    _logger.LogInformation("Failed to get emails for folder");
                }
            }
            else
            {
                _logger.LogError("Fetched folder had a null ID");
            }
        }

        return true;
    }
}
