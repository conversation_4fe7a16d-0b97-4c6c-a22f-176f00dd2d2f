using Azure.Data.Tables;
using FluentResults;

namespace AtveroEmailFiling.Services.AzureServices.TableService
{
    public interface ISyncTableService
    {
        Task<DateTime?> GetLastRetrievalTimeAsync(string userId, string customerDomain);

        Task UpdateLastRetrievalTimeAsync(
            string userId,
            string customerDomain,
            DateTime newRetrievalTime
        );
    }
}
