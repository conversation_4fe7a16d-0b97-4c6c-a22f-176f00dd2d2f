﻿using AtveroEmailFiling.Models.AzureTables;
using Azure;
using Azure.Data.Tables;
using FluentResults;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Services.AzureServices.TableService
{
    public class AzureTableServiceClient : IAzureTableService
    {
        private readonly TableServiceClient _tableServiceClient;
        private readonly ILogger<AzureTableServiceClient> _logger;

        public AzureTableServiceClient(
            string storageUri,
            string accountName,
            string storageAccountKey,
            ILogger<AzureTableServiceClient> logger
        )
        {
            var serviceUri = new Uri(storageUri);
            var storageSharedKeyCredential = new TableSharedKeyCredential(
                accountName,
                storageAccountKey
            );
            _tableServiceClient = new TableServiceClient(serviceUri, storageSharedKeyCredential);
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        // Generate table name in the format {customerdomain}{tablename}
        private static string GetCustomerSpecificTableName(
            string tableNameBase,
            string customerDomain
        )
        {
            return $"{customerDomain}{tableNameBase}";
        }

        public TableClient GetCustomerTableClient(string tableNameBase, string customerDomain)
        {
            var tableName = GetCustomerSpecificTableName(tableNameBase, customerDomain);
            return _tableServiceClient.GetTableClient(tableName);
        }

        public async Task AddEntityAsync<T>(string tableNameBase, T entity, string customerDomain)
            where T : ITableEntity
        {
            try
            {
                var tableClient = GetCustomerTableClient(tableNameBase, customerDomain);
                await tableClient.CreateIfNotExistsAsync();
                await tableClient.AddEntityAsync(entity);
                //_logger.LogInformation($"Added entity to table {tableClient.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    $"Failed to add entity to table {GetCustomerSpecificTableName(tableNameBase, customerDomain)}"
                );
                throw;
            }
        }

        public async Task UpsertEntityAsync<T>(
            string tableNameBase,
            T entity,
            string customerDomain
        )
            where T : ITableEntity
        {
            try
            {
                var tableClient = GetCustomerTableClient(tableNameBase, customerDomain);
                await tableClient.CreateIfNotExistsAsync();
                await tableClient.UpsertEntityAsync(entity);
                // _logger.LogInformation($"Inserted or updated entity to table {tableClient.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogInformation(
                    "Failed to add or update" + JsonConvert.SerializeObject(entity)
                );
                _logger.LogError(
                    ex,
                    $"Failed to add or update entity to table {GetCustomerSpecificTableName(tableNameBase, customerDomain)}"
                );
                throw;
            }
        }

        public async Task<List<T>> QueryEntitiesAsync<T>(
            string tableNameBase,
            string filter,
            string customerDomain
        )
            where T : class, ITableEntity
        {
            var entities = new List<T>();

            try
            {
                var tableClient = GetCustomerTableClient(tableNameBase, customerDomain);
                var query = tableClient.QueryAsync<T>(filter: filter);

                await foreach (var entity in query)
                {
                    entities.Add(entity);
                }

                //_logger.LogInformation($"Queried entities from table {tableClient.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    $"Failed to query entities from table {GetCustomerSpecificTableName(tableNameBase, customerDomain)}"
                );
                throw;
            }

            return entities;
        }

        public async Task UpdateEntityAsync<T>(
            string tableNameBase,
            T entity,
            string customerDomain
        )
            where T : class, ITableEntity
        {
            try
            {
                var tableClient = GetCustomerTableClient(tableNameBase, customerDomain);

                // Directly update the entity using ETag.All to bypass concurrency checks.
                await tableClient.UpdateEntityAsync(entity, ETag.All, TableUpdateMode.Replace);
                //  _logger.LogInformation($"Updated entity in table {tableClient.Name}");
            }
            catch (RequestFailedException ex)
            {
                _logger.LogError(
                    ex,
                    $"Failed to update entity in table {GetCustomerSpecificTableName(tableNameBase, customerDomain)}"
                );
                throw;
            }
        }

        public async Task DeleteEntityAsync(
            string tableNameBase,
            string partitionKey,
            string rowKey,
            string customerDomain
        )
        {
            try
            {
                var tableClient = GetCustomerTableClient(tableNameBase, customerDomain);
                await tableClient.DeleteEntityAsync(partitionKey, rowKey);
                _logger.LogInformation($"Deleted entity from table {tableClient.Name}");
            }
            catch (RequestFailedException ex)
            {
                _logger.LogError(
                    ex,
                    $"Failed to delete entity from table {GetCustomerSpecificTableName(tableNameBase, customerDomain)}"
                );
                throw;
            }
        }

        public async Task<Result<T>> GetEntityAsync<T>(
            string tableNameBase,
            string partitionKey,
            string rowKey,
            string customerDomain
        )
            where T : class, ITableEntity
        {
            try
            {
                var tableClient = GetCustomerTableClient(tableNameBase, customerDomain);
                var response = await tableClient.GetEntityAsync<T>(partitionKey, rowKey);

                if (response != null && response.Value != null)
                {
                    //  _logger.LogInformation($"Retrieved entity from table {tableClient.Name}");
                    return response.Value;
                }
                else
                {
                    return Result.Fail("Entity not found in table.");
                }
            }
            catch (RequestFailedException ex) when (ex.Status == 404)
            {
                // _logger.LogWarning($"Entity not found in table.");
                return Result.Fail("Entity not found in table.");
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    $"Failed to retrieve entity from table {GetCustomerSpecificTableName(tableNameBase, customerDomain)}"
                );
                return Result.Fail(
                    $"Failed to retrieve entity from table {GetCustomerSpecificTableName(tableNameBase, customerDomain)}"
                );
            }
        }

        public async Task AddEntitiesBatchAsync(
            string tableNameBase,
            IEnumerable<ITableEntity> entities,
            string customerDomain
        )
        {
            try
            {
                var tableClient = GetCustomerTableClient(tableNameBase, customerDomain);
                await tableClient.CreateIfNotExistsAsync();
                var partitionedEntities = entities.GroupBy(e => e.PartitionKey);

                foreach (var partition in partitionedEntities)
                {
                    var batchOperation = new List<TableTransactionAction>();
                    foreach (var entity in partition)
                    {
                        batchOperation.Add(
                            new TableTransactionAction(TableTransactionActionType.Add, entity)
                        );
                    }
                    await tableClient.SubmitTransactionAsync(batchOperation);
                }

                // _logger.LogInformation($"Added batch of entities to table {tableClient.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    $"Failed to add batch to table {GetCustomerSpecificTableName(tableNameBase, customerDomain)}"
                );
                throw;
            }
        }

        public async Task<bool> EntityExistsAsync<T>(
            string tableNameBase,
            string partitionKey,
            string rowKey,
            string customerDomain
        )
            where T : class, ITableEntity, new()
        {
            Result<T> entity = await GetEntityAsync<T>(
                tableNameBase,
                partitionKey,
                rowKey,
                customerDomain
            );
            return entity.IsSuccess;
        }
    }
}
