﻿using Azure.Data.Tables;
using FluentResults;

namespace AtveroEmailFiling.Services.AzureServices.TableService
{
    public interface IAzureTableService
    {
        TableClient GetCustomerTableClient(string tableNameBase, string customerDomain);
        Task AddEntityAsync<T>(string tableNameBase, T entity, string customerDomain)
            where T : ITableEntity;

        Task UpsertEntityAsync<T>(string tableNameBase, T entity, string customerDomain)
            where T : ITableEntity;
        Task<List<T>> QueryEntitiesAsync<T>(
            string tableNameBase,
            string filter,
            string customerDomain
        )
            where T : class, ITableEntity;
        Task UpdateEntityAsync<T>(string tableNameBase, T entity, string customerDomain)
            where T : class, ITableEntity;

        Task DeleteEntityAsync(
            string tableNameBase,
            string partitionKey,
            string rowKey,
            string customerDomain
        );
        Task<Result<T>> GetEntityAsync<T>(
            string tableNameBase,
            string partitionKey,
            string rowKey,
            string customerDomain
        )
            where T : class, ITableEntity;
        Task AddEntitiesBatchAsync(
            string tableNameBase,
            IEnumerable<ITableEntity> entities,
            string customerDomain
        );

        Task<bool> EntityExistsAsync<T>(
            string tableNameBase,
            string partitionKey,
            string rowKey,
            string customerDomain
        )
            where T : class, ITableEntity, new();
    }
}
