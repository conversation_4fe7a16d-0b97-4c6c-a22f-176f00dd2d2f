﻿using Azure.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;

namespace AtveroEmailFiling.Services.MSGraphClientService
{
    public class GraphClientService : IGraphClientService, IDisposable
    {
        private readonly IConfiguration _config;
        private readonly ILogger _logger;
        private GraphServiceClient? _appGraphClient;
        private bool _disposed = false;

        public GraphClientService(IConfiguration config, ILoggerFactory loggerFactory)
        {
            _config = config;
            _logger = loggerFactory.CreateLogger<GraphClientService>();
        }

        public GraphServiceClient? GetUserGraphClient(
            string userAssertion,
            string clientId,
            string clientSecret
        )
        {
            var tenantId = _config["tenantId"];

            if (
                string.IsNullOrEmpty(tenantId)
                || string.IsNullOrEmpty(clientId)
                || string.IsNullOrEmpty(clientSecret)
            )
            {
                _logger.LogError(
                    "GetUserGraphClient: Required settings missing: 'tenantId', 'apiClientId', and 'apiClientSecret'."
                );
                return null;
            }

            var onBehalfOfCredential = new OnBehalfOfCredential(
                tenantId,
                clientId,
                clientSecret,
                userAssertion
            );

            return new GraphServiceClient(onBehalfOfCredential);
        }

        public string? GetTenant()
        {
            return _config["tenantId"];
        }

        public GraphServiceClient? GetAppGraphClient()
        {
            if (_appGraphClient == null)
            {
                var tenantId = _config["tenantId"];
                var clientId = _config["webhookClientId"];
                var clientSecret = _config["webhookClientSecret"];

                if (
                    string.IsNullOrEmpty(tenantId)
                    || string.IsNullOrEmpty(clientId)
                    || string.IsNullOrEmpty(clientSecret)
                )
                {
                    _logger.LogError(
                        "GetAppGraphClient: Required settings missing: 'tenantId', 'webhookClientId', and 'webhookClientSecret'."
                    );
                    return null;
                }

                var clientSecretCredential = new ClientSecretCredential(
                    tenantId,
                    clientId,
                    clientSecret
                );

                _appGraphClient = new GraphServiceClient(clientSecretCredential);
            }

            return _appGraphClient;
        }

        // Implement IDisposable
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    if (_appGraphClient != null)
                    {
                        (_appGraphClient as IDisposable)?.Dispose();
                        _appGraphClient = null;
                    }
                }

                // Dispose unmanaged resources if any

                _disposed = true;
            }
        }

        ~GraphClientService()
        {
            Dispose(false);
        }
    }
}
