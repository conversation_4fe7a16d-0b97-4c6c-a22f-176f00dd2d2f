﻿using AtveroEmailFiling.Models;
using AtveroEmailFiling.Services.EmailMetadata;
using FluentResults;
using Microsoft.Graph;

namespace AtveroEmailFiling.Services.EmailUploading
{
    public interface IEmailUploadingService
    {
        Task<Result> ProcessMessagesAsync(
            List<(OutlookMailMessage msg, string filingPath, string messageId)> messagesToFile,
            string driveId,
            GraphServiceClient graphClient,
            MetaData metadata,
            IEmailMetadataService emailMetadataService,
            string filedBy,
            string customerDomain,
            string filingTag,
            string activeUser,
            string userName,
            string? movetoFiledFolder
        );
        Task<Result> GenerateAndUploadFileAsync(
            string driveId,
            string filingPath,
            string messageId,
            OutlookMailMessage msg,
            GraphServiceClient graphClient,
            MetaData metadata,
            string activeUser
        );
        Task<Result> SetMetadataForUploadedFileAsync(
            string driveId,
            string itemId,
            OutlookMailMessage email,
            GraphServiceClient graphClient,
            MetaData metadata
        );
    }
}
