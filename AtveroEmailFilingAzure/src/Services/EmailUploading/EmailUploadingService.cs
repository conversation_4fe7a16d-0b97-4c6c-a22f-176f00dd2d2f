﻿using AtveroEmailFiling.Models;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.EmailMetadata;
using AtveroEmailFiling.Services.FilingStatusCheck;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Utils;
using FluentResults;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Newtonsoft.Json;
using Sentry.Azure.Functions.Worker;

namespace AtveroEmailFiling.Services.EmailUploading
{
    public class EmailUploadingService : IEmailUploadingService
    {
        private readonly ILogger<EmailUploadingService> _logger;
        private readonly IGraphApiClient _graphApiClient;
        private readonly IFilingStatusCheckService _filingStatusCheckService;
        private readonly IAzureTableService _tableService;

        public EmailUploadingService(
            ILogger<EmailUploadingService> logger,
            IGraphApiClient graphApiClient,
            IFilingStatusCheckService filingStatusCheckService,
            IAzureTableService tableService
        )
        {
            _logger = logger;
            _graphApiClient = graphApiClient;
            _filingStatusCheckService = filingStatusCheckService;
            _tableService = tableService;
        }

        public async Task<Result> ProcessMessagesAsync(
            List<(OutlookMailMessage msg, string filingPath, string messageId)> messagesToFile,
            string driveId,
            GraphServiceClient graphClient,
            MetaData metadata,
            IEmailMetadataService emailMetadataService,
            string filedBy,
            string customerDomain,
            string filingTag,
            string activeUser,
            string userName,
            string? movetoFiledFolder
        )
        {
            bool atLeastOneUploadFailed = false;

            int filed = 0;

            foreach (var (msg, filingPath, messageId) in messagesToFile)
            {
                try
                {
                    Result uploaded = await GenerateAndUploadFileAsync(
                        driveId,
                        filingPath,
                        messageId,
                        msg,
                        graphClient,
                        metadata,
                        activeUser
                    );

                    if (uploaded.IsSuccess && metadata.ProjectCode != null)
                    {
                        await emailMetadataService.SetEmailMetadata(
                            messageId,
                            metadata.ProjectCode,
                            graphClient,
                            metadata.Tag,
                            metadata.Important,
                            metadata.Confidential,
                            filingTag,
                            filedBy,
                            activeUser
                        );

                        if (movetoFiledFolder != null)
                        {
                            await EmailUtils.MoveFiledEmailToFolder(
                                graphClient,
                                activeUser,
                                messageId,
                                movetoFiledFolder,
                                _logger
                            );
                        }

                        filed++;

                        try
                        {
                            if (msg.InternetMessageId != null)
                            {
                                FiledEmail filedEmail = new FiledEmail()
                                {
                                    PartitionKey = EmailUtils.CleanMessageId(msg.InternetMessageId),
                                    RowKey = FiledEmail.generateRowKey(),
                                    ProjectCode = metadata.ProjectCode,
                                    SitePath = metadata.SitePath,
                                    Tag = metadata.Tag,
                                    Confidential = metadata.Confidential,
                                    FiledBy = filedBy,
                                    FiledByEmail = activeUser,
                                    Important = metadata.Important,
                                };

                                _logger.LogInformation(
                                    "Upserting " + JsonConvert.SerializeObject(filedEmail)
                                );

                                await _filingStatusCheckService.UpsertFiledMessageAsync(
                                    filedEmail,
                                    customerDomain
                                );

                                TimeFiledEmail timeFiledEmail = new TimeFiledEmail()
                                {
                                    PartitionKey = filedEmail.RowKey,
                                    RowKey = EmailUtils.CleanMessageId(msg.InternetMessageId),
                                    SitePath = filedEmail.SitePath,
                                };

                                await _filingStatusCheckService.UpsertTimeFiledMessageAsync(
                                    timeFiledEmail,
                                    customerDomain
                                );
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogInformation(
                                "Can't add one of the file email messages to table"
                            );
                            _logger.LogError(
                                ex,
                                "Can't add one of the file email messages to table"
                            );
                        }
                    }
                    else
                    {
                        atLeastOneUploadFailed = true;
                    }

                    // collect stats

                    _logger.LogInformation("Collecting stats " + filingTag);

                    if (filed > 0)
                    {
                        if (filingTag.StartsWith("Auto"))
                        {
                            await StatsUtils.LogMessageFiledAutomaticallyForUser(
                                _tableService,
                                customerDomain,
                                activeUser,
                                filed,
                                _logger
                            );
                        }

                        if (filingTag.StartsWith("Filed"))
                        {
                            await StatsUtils.LogMessageFiledForUser(
                                _tableService,
                                customerDomain,
                                activeUser,
                                filed,
                                _logger
                            );
                        }

                        if (metadata.ProjectCode != null)
                        {
                            await StatsUtils.LogMessageFiledByProject(
                                _tableService,
                                customerDomain,
                                metadata.ProjectCode,
                                filed,
                                _logger
                            );
                        }

                        await StatsUtils.LogMessageFiledForDomain(
                            _tableService,
                            customerDomain,
                            filed,
                            _logger
                        );
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogInformation(
                        $"Exception occurred while processing message ID: {messageId}. Continuing with next message."
                            + ex.Message
                    );
                    _logger.LogError(ex, $"Exception occurred while processing message");
                    atLeastOneUploadFailed = true;
                }
            }

            if (atLeastOneUploadFailed)
                return Result.Fail("At least one upload failed");

            return Result.Ok();
        }

        public async Task<Result> GenerateAndUploadFileAsync(
            string driveId,
            string filingPath,
            string messageId,
            OutlookMailMessage msg,
            GraphServiceClient graphClient,
            MetaData metadata,
            string activeUser
        )
        {
            try
            {
                // _logger.LogInformation(
                //     $"Generating and uploading file for message ID: {messageId} to path: {filingPath} in drive: {driveId}"
                // );

                // Get the message content stream
                Stream? messageContentStream = await _graphApiClient.GetMessageContentAsync(
                    messageId,
                    graphClient,
                    activeUser
                );
                // logger.LogInformation(
                //     $"Retrieved message content stream for message ID: {messageId}"
                // );

                if (messageContentStream != null)
                {
                    // Convert the content stream to a byte array
                    byte[] fileContent;
                    using (var memoryStream = new MemoryStream())
                    {
                        await messageContentStream.CopyToAsync(memoryStream);
                        fileContent = memoryStream.ToArray();
                    }
                    // logger.LogInformation(
                    //     $"Converted message content stream to byte array for message ID: {messageId}"
                    // );

                    // Upload the file content to the specified path in the drive
                    using (var fileStream = new MemoryStream(fileContent))
                    {
                        DriveItem? uploadedFile;
                        try
                        {
                            uploadedFile = await _graphApiClient.UploadFileAsync(
                                driveId,
                                filingPath,
                                fileStream,
                                graphClient
                            );
                        }
                        catch (Exception ex)
                        {
                            if (
                                ex.Message.Contains("Name already exists")
                                || ex.Message.Contains(
                                    "The resource has changed since the caller last read it"
                                )
                            )
                            {
                                _logger.LogInformation(
                                    "File already exists, someone beat us to it?"
                                );
                                try
                                {
                                    var retry = await _graphApiClient.GetDriveItemByPathAsync(
                                        driveId,
                                        filingPath,
                                        graphClient
                                    );

                                    if (retry != null)
                                    {
                                        return Result.Ok();
                                    }
                                    return Result.Fail("Failed to upload message content");
                                }
                                catch (Exception e2x)
                                {
                                    _logger.LogInformation(
                                        e2x,
                                        "Tried to upload, already existed, tried to download but failed"
                                    );
                                    _logger.LogError("Failed to upload due to race condition");
                                    return Result.Fail("Failed to upload message content");
                                }
                            }
                            else
                            {
                                _logger.LogInformation("Failed uploading to " + filingPath);
                                _logger.LogError(ex, "Failed to upload file");
                                return Result.Fail("Failed to upload message content");
                            }
                        }

                        if (uploadedFile != null && uploadedFile.Id != null)
                        {
                            _logger.LogInformation(
                                $"File uploaded: {uploadedFile.Name}, {uploadedFile.Size} bytes"
                            );

                            // Set metadata for the uploaded file
                            try
                            {
                                Result res = await SetMetadataForUploadedFileAsync(
                                    driveId,
                                    uploadedFile.Id,
                                    msg,
                                    graphClient,
                                    metadata
                                );

                                if (res.IsSuccess)
                                    return Result.Ok();

                                _logger.LogInformation(
                                    "Deleting uploaded file as failed to set metadata"
                                );

                                await _graphApiClient.DeleteFileAsync(
                                    driveId,
                                    filingPath,
                                    graphClient
                                );

                                return Result.Fail(
                                    "Deleted uploaded message as metadata didn't set"
                                );
                            }
                            catch (Exception ex)
                            {
                                _logger.LogInformation(ex.Message);

                                _logger.LogInformation(
                                    $"Exception occurred while setting metadata for file with ID: {uploadedFile.Id} in drive: {driveId}. Metadata setting will be retried separately."
                                        + ex.Message
                                );

                                await _graphApiClient.DeleteFileAsync(
                                    driveId,
                                    filingPath,
                                    graphClient
                                );

                                return Result.Fail(
                                    "Deleted uploaded message as metadata didn't set"
                                );
                            }
                        }
                        else
                        {
                            return Result.Fail("Failed to upload message content");
                        }
                    }
                }
                else
                {
                    return Result.Fail("Failed to get message content");
                }
            }
            catch (Exception ex)
            {
                // _logger.LogError(
                //     $"Exception occurred while generating and uploading file for message ID: {messageId} to path: {filingPath} in drive: {driveId} "
                //         + ex.Message
                // );
                _logger.LogInformation(
                    ex,
                    $"Exception occurred while generating and uploading file for message ID: {messageId} to path: {filingPath} in drive: {driveId} "
                );
                _logger.LogError(ex, "Exception occurred while generating and uploading file");

                return Result.Fail("Failed uploading message content");
            }
        }

        public async Task<Result> SetMetadataForUploadedFileAsync(
            string driveId,
            string itemId,
            OutlookMailMessage email,
            GraphServiceClient graphClient,
            MetaData metadata
        )
        {
            try
            {
                _logger.LogInformation(
                    $"Setting metadata for uploaded file with ID: {itemId} in drive: {driveId}"
                );

                string subject = email.Subject ?? "No Subject";
                if (subject.Length > 221)
                    subject = subject.Substring(0, 220);

                string recipients = email.To ?? "No Recipients";
                if (recipients.Length > 221)
                    recipients = recipients.Substring(0, 220);

                string from = email.From ?? "No Sender";
                if (from.Length > 221)
                    from = from.Substring(0, 220);

                var fields = new FieldValueSet
                {
                    AdditionalData = new Dictionary<string, object>
                    {
                        { "Title", subject },
                        { "EmailSubject", subject },
                        { "EmailFrom", from },
                        { "EmailTo", recipients },
                        { "ATVSearchRefiner", email.SearchRefiner },
                        { "AttachmentCount", email.AttachmentCount },
                        { "EmailImportant", metadata.Important ? "Yes" : "No" },
                    },
                };

                if (email.ReceivedOn != null)
                {
                    fields.AdditionalData.Add("EmailReceivedOn", email.ReceivedOn?.ToString("o"));
                    fields.AdditionalData.Add("EmailReceived", email.EmailReceived?.ToString("o"));
                    fields.AdditionalData.Add("ReceivedOn", email.ReceivedOn?.ToString("o"));
                }

                if (metadata.Tag != null)
                {
                    fields.AdditionalData.Add("EmailTags", metadata.Tag);
                }

                if (email.Cc != null)
                {
                    string ccstring = email.Cc;
                    if (ccstring.Length > 221)
                    {
                        ccstring = ccstring.Substring(0, 220);
                    }
                    fields.AdditionalData.Add("EmailCC", ccstring);
                }

                if (email.TextSummary != null)
                {
                    string summary = email.TextSummary;

                    if (summary.Length > 221)
                    {
                        summary = summary.Substring(0, 220);
                    }
                    fields.AdditionalData.Add("EmailTextSummary", summary);
                }

                if (email.InternetMessageId != null)
                {
                    fields.AdditionalData.Add(
                        "ATVMessageId",
                        EmailUtils.CleanMessageId(email.InternetMessageId)
                    );
                }

                if (email.ConversationId != null)
                {
                    fields.AdditionalData.Add("ATVConversationId", email.ConversationId);
                }

                var listItem = await _graphApiClient.GetListItemAsync(driveId, itemId, graphClient);

                if (
                    listItem?.Id != null
                    && listItem.ParentReference?.SiteId != null
                    && listItem.SharepointIds?.ListId != null
                )
                {
                    // retry

                    bool updated = false;
                    int retry = 0;

                    while (!updated && retry < 5)
                    {
                        try
                        {
                            updated = await _graphApiClient.PatchListItemFieldsAsync(
                                listItem.ParentReference.SiteId,
                                listItem.SharepointIds.ListId,
                                listItem.Id,
                                fields,
                                graphClient
                            );

                            if (!updated)
                            {
                                _logger.LogInformation(
                                    "Failed to patch with fields: "
                                        + JsonConvert.SerializeObject(fields)
                                );
                            }
                            retry++;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogInformation(
                                ex,
                                "Exceptiion - Failed to patch with fields: "
                                    + JsonConvert.SerializeObject(fields)
                            );
                            retry++;
                        }
                    }

                    if (updated)
                        return Result.Ok();
                    else
                    {
                        _logger.LogInformation(
                            "Failed to patch with fields: " + JsonConvert.SerializeObject(fields)
                        );
                        _logger.LogError("Failed to patch list item for metadata update");

                        return Result.Fail(
                            $"Error setting metadata for file with ID: {itemId} in drive: {driveId} "
                        );
                    }
                }
                else
                {
                    _logger.LogWarning(
                        $"Failed to open drive item {itemId} in drive: {driveId} to update metadata"
                    );

                    SentrySdk.CaptureMessage(
                        $"Failed to open drive item {itemId} in drive: {driveId} to update metadata"
                    );
                    return Result.Fail(
                        $"Failed to open drive item {itemId} in drive: {driveId} to update metadata"
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation(
                    $"Exception occurred while setting metadata for file with ID: {itemId} in drive: {driveId} "
                        + ex.Message
                );

                _logger.LogError(ex, "Exception occurred while setting metadata for file");
                return Result.Fail(
                    $"Exception occurred while setting metadata for file with ID: {itemId} in drive: {driveId} "
                        + ex.Message
                );
            }
        }
    }
}
