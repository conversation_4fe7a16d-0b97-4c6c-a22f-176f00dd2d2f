﻿using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Services.MSGraphClientService;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;

namespace AtveroEmailFiling.Services.FavService
{
    /// <summary>
    /// Service to manage favorites.
    /// </summary>
    public class FavouritesService : IFavouritesService
    {
        private readonly ILogger<FavouritesService> _logger;
        private readonly IGraphClientService _graphClientService;
        private const string JsonMediaType = "application/json";
        private const string EmptyJsonArray = "[]";
        private const string AppFolderName = "FavoritesApp";
        private const string SpecialFolderName = "Apps";
        private readonly HttpClient _httpClient;
        private const string GraphApiBaseUrl =
            "https://graph.microsoft.com/v1.0/me/drive/special/approot:/";

        /// <summary>
        /// Initializes a new instance of the <see cref="FavouritesService"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="graphClientService">The Graph client service instance.</param>
        public FavouritesService(
            ILogger<FavouritesService> logger,
            IGraphClientService graphClientService,
            HttpClient httpClient
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        }

        /// <summary>
        /// Gets all favorites asynchronously.
        /// </summary>
        /// <param name="fileName">The file name containing favorites.</param>
        /// <param name="accessToken">The access token for authentication.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task<string> GetAllFavoritesAsync(
            string fileName,
            string accessToken,
            string clientId,
            string clientSecret
        )
        {
            try
            {
                GraphServiceClient? graphClient = _graphClientService.GetUserGraphClient(
                    accessToken,
                    clientId,
                    clientSecret
                );
                if (graphClient == null)
                {
                    throw new Exception("Unable to acquire Graph client");
                }
                return await ReadConfigFileAsync(fileName, accessToken, graphClient);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    $"An error occurred while getting all favorites from '{fileName}'."
                );
                throw;
            }
        }

        /// <summary>
        /// Reads the configuration file asynchronously.
        /// </summary>
        /// <param name="fileName">The file name to read.</param>
        /// <param name="token">The access token for authentication.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task<string> ReadConfigFileAsync(
            string fileName,
            string token,
            GraphServiceClient graphClient
        )
        {
            try
            {
                DriveCollectionResponse? drives = await graphClient.Me.Drives.GetAsync();

                var oneDrive = drives?.Value?.FirstOrDefault(drive =>
                    drive.Name != null
                    && drive.Description != null
                    && drive.Name.Equals("OneDrive", StringComparison.OrdinalIgnoreCase)
                    && !drive.Description.Equals(
                        "Personal Cache List",
                        StringComparison.OrdinalIgnoreCase
                    )
                );

                if (oneDrive?.Id == null)
                {
                    _logger.LogWarning("OneDrive not found or only cache drive found.");
                    throw new Exception("OneDrive not found.");
                }

                DriveItem? driveItem = await graphClient
                    .Drives[oneDrive.Id]
                    .Special["Apps"]
                    .GetAsync();

                if (driveItem?.Id == null)
                {
                    _logger.LogWarning("Apps folder not found.");
                    throw new Exception("Apps folder not found.");
                }

                DriveItemCollectionResponse? driveItems = await graphClient
                    .Drives[oneDrive.Id]
                    .Items[driveItem.Id]
                    .Children.GetAsync();

                var appFolderItem = driveItems?.Value?.FirstOrDefault(item =>
                    item.Name.Equals(AppFolderName, StringComparison.OrdinalIgnoreCase)
                );

                if (appFolderItem?.Id == null)
                {
                    _logger.LogWarning($"{AppFolderName} folder not found. Creating a new one.");
                    appFolderItem = await CreateAppFolderAsync(
                        graphClient,
                        oneDrive.Id,
                        driveItem.Id
                    );
                }

                if (appFolderItem?.Id == null)
                {
                    _logger.LogWarning("Apps folder not found after trying to make one.");
                    throw new Exception("Apps folder not found after trying to make one.");
                }

                var folderItems = await graphClient
                    .Drives[oneDrive.Id]
                    .Items[appFolderItem.Id]
                    .Children.GetAsync();

                var favouritesFile = folderItems?.Value?.FirstOrDefault(item =>
                    item.Name == fileName
                );

                if (favouritesFile == null)
                {
                    _logger.LogWarning($"File '{fileName}' not found. Creating a new file.");
                    await CreateEmptyFileAsync(
                        fileName,
                        graphClient,
                        token,
                        appFolderItem.Id,
                        oneDrive.Id
                    );
                    return EmptyJsonArray;
                }

                Stream? fileContentStream = await graphClient
                    .Drives[oneDrive.Id]
                    .Items[favouritesFile.Id]
                    .Content.GetAsync();

                if (fileContentStream != null)
                {
                    using (var reader = new StreamReader(fileContentStream))
                    {
                        var content = await reader.ReadToEndAsync();
                        return content;
                    }
                }
                else
                {
                    throw new Exception("Can't stream settings file content");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unexpected error occurred while reading the config file.");
                throw;
            }
        }

        /// <summary>
        /// Creates the application folder asynchronously.
        /// </summary>
        /// <param name="graphClient">The Graph client instance.</param>
        /// <param name="driveId">The drive ID.</param>
        /// <param name="parentId">The parent ID.</param>
        /// <returns>The created folder item.</returns>
        private async Task<DriveItem?> CreateAppFolderAsync(
            GraphServiceClient graphClient,
            string driveId,
            string parentId
        )
        {
            var folderToCreate = new DriveItem { Name = AppFolderName, Folder = new Folder() };

            return await graphClient
                .Drives[driveId]
                .Items[parentId]
                .Children.PostAsync(folderToCreate);
        }

        /// <summary>
        /// Creates an empty file asynchronously.
        /// </summary>
        /// <param name="fileName">The file name to create.</param>
        /// <param name="token">The access token for authentication.</param>
        /// <param name="parentId">The parent folder ID.</param>
        /// <param name="driveId">The drive ID.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task CreateEmptyFileAsync(
            string fileName,
            GraphServiceClient graphClient,
            string token,
            string parentId,
            string driveId
        )
        {
            try
            {
                var newFile = new Microsoft.Graph.Models.DriveItem
                {
                    Name = fileName,
                    File = new Microsoft.Graph.Models.FileObject(), // Assuming FileObject is the correct type
                };

                var createdFile = await graphClient
                    .Drives[driveId]
                    .Items[parentId]
                    .Children.PostAsync(newFile);

                var requestUrl = $"{GraphApiBaseUrl}{fileName}:/content";
                var request = new HttpRequestMessage(HttpMethod.Put, requestUrl)
                {
                    Headers = { Authorization = new AuthenticationHeaderValue("Bearer", token) },
                    Content = new StringContent(EmptyJsonArray, Encoding.UTF8, JsonMediaType),
                };

                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();
                _logger.LogInformation($"Empty file '{fileName}' created successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogInformation(
                    ex,
                    $"Failed to create a new Empty file '{fileName}' but continuing"
                );
            }
        }

        /// <summary>
        /// Adds favorites asynchronously.
        /// </summary>
        /// <param name="fileName">The file name containing favorites.</param>
        /// <param name="jsonContent">The JSON content to add.</param>
        /// <param name="accessToken">The access token for authentication.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task AddFavoritesAsync(
            string fileName,
            string jsonContent,
            string accessToken,
            string clientId,
            string clientSecret
        )
        {
            try
            {
                GraphServiceClient? graphClient = _graphClientService.GetUserGraphClient(
                    accessToken,
                    clientId,
                    clientSecret
                );
                if (graphClient == null)
                {
                    throw new Exception("Unable to acquire Graph client");
                }
                var existingContent = await ReadConfigFileAsync(fileName, accessToken, graphClient);
                var existingFavorites = string.IsNullOrEmpty(existingContent)
                    ? new List<Favourite>()
                    : JsonSerializer.Deserialize<List<Favourite>>(existingContent);

                var newFavorites = JsonSerializer.Deserialize<List<Favourite>>(jsonContent);

                if (newFavorites != null)
                {
                    foreach (var newFavorite in newFavorites)
                    {
                        if (
                            !existingFavorites.Any(f =>
                                f.ProjectCode == newFavorite.ProjectCode
                                && f.ProjectTitle == newFavorite.ProjectTitle
                            )
                        )
                        {
                            existingFavorites.Add(newFavorite);
                        }
                        else
                        {
                            _logger.LogInformation(
                                $"Favorite '{newFavorite.ProjectTitle}' already exists."
                            );
                        }
                    }

                    var updatedJsonContent = JsonSerializer.Serialize(existingFavorites);
                    await SaveConfigFileAsync(fileName, updatedJsonContent, graphClient);
                }
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Error deserializing content.");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while adding favorites.");
                throw;
            }
        }

        /// <summary>
        /// Deletes favorites asynchronously.
        /// </summary>
        /// <param name="fileName">The file name containing favorites.</param>
        /// <param name="jsonContent">The JSON content to delete.</param>
        /// <param name="accessToken">The access token for authentication.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task DeleteFavoritesAsync(
            string fileName,
            string jsonContent,
            string accessToken,
            string clientId,
            string clientSecret
        )
        {
            try
            {
                GraphServiceClient? graphClient = _graphClientService.GetUserGraphClient(
                    accessToken,
                    clientId,
                    clientSecret
                );
                if (graphClient == null)
                {
                    throw new Exception("Unable to acquire Graph client");
                }
                var existingContent = await ReadConfigFileAsync(fileName, accessToken, graphClient);
                var existingFavorites = JsonSerializer.Deserialize<List<Favourite>>(
                    existingContent
                );
                var favoritesToDelete = JsonSerializer.Deserialize<List<Favourite>>(jsonContent);

                foreach (var favoriteToDelete in favoritesToDelete)
                {
                    var existingFavorite = existingFavorites.FirstOrDefault(f =>
                        f.ProjectCode == favoriteToDelete.ProjectCode
                        && f.ProjectTitle == favoriteToDelete.ProjectTitle
                    );
                    if (existingFavorite != null)
                    {
                        existingFavorites.Remove(existingFavorite);
                        _logger.LogInformation(
                            $"Favorite '{favoriteToDelete.ProjectTitle}' deleted successfully."
                        );
                    }
                    else
                    {
                        _logger.LogInformation(
                            $"Favorite '{favoriteToDelete.ProjectTitle}' does not exist."
                        );
                    }
                }

                var updatedJsonContent = JsonSerializer.Serialize(existingFavorites);
                await SaveConfigFileAsync(fileName, updatedJsonContent, graphClient);
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Error deserializing content.");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while deleting favorites.");
                throw;
            }
        }

        /// <summary>
        /// Saves the configuration file asynchronously.
        /// </summary>
        /// <param name="fileName">The file name to save.</param>
        /// <param name="jsonContent">The JSON content to save.</param>
        /// <param name="token">The access token for authentication.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task SaveConfigFileAsync(
            string fileName,
            string jsonContent,
            GraphServiceClient graphClient
        )
        {
            try
            {
                var drives = await graphClient.Me.Drives.GetAsync();

                var oneDrive = drives.Value.FirstOrDefault(drive =>
                    drive.Name.Equals("OneDrive", StringComparison.OrdinalIgnoreCase)
                    && !drive.Description.Equals(
                        "Personal Cache List",
                        StringComparison.OrdinalIgnoreCase
                    )
                );

                if (oneDrive == null)
                {
                    _logger.LogWarning("OneDrive not found or only cache drive found.");
                    throw new Exception("OneDrive not found.");
                }

                // Get or create the app folder
                var appFolderItem = await GetOrCreateFolderAsync(
                    graphClient,
                    oneDrive.Id,
                    SpecialFolderName,
                    AppFolderName
                );

                if (appFolderItem == null)
                {
                    throw new Exception($"Could not create or access the {AppFolderName} folder.");
                }

                // Create or update the config file
                var fileContentStream = new MemoryStream(Encoding.UTF8.GetBytes(jsonContent));
                var driveItem = await graphClient
                    .Drives[oneDrive.Id]
                    .Items[appFolderItem.Id]
                    .ItemWithPath(fileName)
                    .Content.PutAsync(fileContentStream);

                _logger.LogInformation($"File '{fileName}' saved successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while saving the config file.");
                throw;
            }
        }

        private async Task<Microsoft.Graph.Models.DriveItem> GetOrCreateFolderAsync(
            GraphServiceClient graphClient,
            string driveId,
            string parentFolderName,
            string folderName
        )
        {
            try
            {
                // Get the parent folder
                var parentFolder = await graphClient
                    .Drives[driveId]
                    .Special[parentFolderName]
                    .GetAsync();

                // Check if the folder exists
                var folderItems = await graphClient
                    .Drives[driveId]
                    .Items[parentFolder.Id]
                    .Children.GetAsync();

                var folderItem = folderItems.Value.FirstOrDefault(item =>
                    item.Name.Equals(folderName, StringComparison.OrdinalIgnoreCase)
                );

                if (folderItem == null)
                {
                    // Create the folder if it doesn't exist
                    var newFolder = new Microsoft.Graph.Models.DriveItem
                    {
                        Name = folderName,
                        Folder = new Microsoft.Graph.Models.Folder(),
                    };

                    folderItem = await graphClient
                        .Drives[driveId]
                        .Items[parentFolder.Id]
                        .Children.PostAsync(newFolder);

                    _logger.LogInformation($"Folder '{folderName}' created successfully.");
                }

                return folderItem;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    $"An error occurred while accessing or creating the folder '{folderName}'."
                );
                throw;
            }
        }
    }
}
