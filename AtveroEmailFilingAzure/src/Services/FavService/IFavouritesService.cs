﻿namespace AtveroEmailFiling.Services.FavService
{
    /// <summary>
    /// Interface for managing favourite items.
    /// </summary>
    public interface IFavouritesService
    {
        /// <summary>
        /// Retrieves all favourite items asynchronously.
        /// </summary>
        /// <param name="fileName">The name of the file containing the favourites.</param>
        /// <param name="accessToken">The access token for authentication.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the favourites in JSON format.</returns>
        Task<string> GetAllFavoritesAsync(
            string fileName,
            string accessToken,
            string clientId,
            string clientSecret
        );

        /// <summary>
        /// Adds favourite items asynchronously.
        /// </summary>
        /// <param name="fileName">The name of the file to which the favourites will be added.</param>
        /// <param name="jsonContent">The JSON content of the favourite items to add.</param>
        /// <param name="accessToken">The access token for authentication.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task AddFavoritesAsync(
            string fileName,
            string jsonContent,
            string accessToken,
            string clientId,
            string clientSecret
        );

        /// <summary>
        /// Deletes favourite items asynchronously.
        /// </summary>
        /// <param name="fileName">The name of the file from which the favourites will be deleted.</param>
        /// <param name="jsonContent">The JSON content of the favourite items to delete.</param>
        /// <param name="accessToken">The access token for authentication.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task DeleteFavoritesAsync(
            string fileName,
            string jsonContent,
            string accessToken,
            string clientId,
            string clientSecret
        );
    }
}
