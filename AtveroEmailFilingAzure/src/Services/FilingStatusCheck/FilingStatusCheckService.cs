﻿using System.Globalization;
using System.Net;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Utils;
using Azure.Data.Tables;
using FluentResults;
using Microsoft.Graph;

namespace AtveroEmailFiling.Services.FilingStatusCheck
{
    public class FilingStatusCheckService : IFilingStatusCheckService
    {
        private const string ConversationIdField = "ConversationId";
        private const string ProjectCodeField = "ProjectCode";
        private const string DateTimeFormat = "yyyyMMddHHmmss";

        private readonly IAzureTableService _tableServiceClient;
        private readonly IGraphApiClient _graphApiClient;

        public FilingStatusCheckService(
            IAzureTableService tableServiceClient,
            IGraphApiClient graphApiClient
        )
        {
            _tableServiceClient =
                tableServiceClient ?? throw new ArgumentNullException(nameof(tableServiceClient));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
        }

        public async Task AddConversationAsync(Conversation conversation, string customerDomain)
        {
            // Use a customer-specific table for Conversations
            await _tableServiceClient.AddEntityAsync("Conversations", conversation, customerDomain);
        }

        public async Task UpsertConversationAsync(Conversation conversation, string customerDomain)
        {
            // Use a customer-specific table for Conversations
            await _tableServiceClient.UpsertEntityAsync(
                "Conversations",
                conversation,
                customerDomain
            );
        }

        public async Task UpsertFiledMessageAsync(FiledEmail filedEmail, string customerDomain)
        {
            // Use a customer-specific table for Conversations
            await _tableServiceClient.UpsertEntityAsync("FiledEmails", filedEmail, customerDomain);
        }

        public async Task UpsertTimeFiledMessageAsync(
            TimeFiledEmail filedEmail,
            string customerDomain
        )
        {
            // Use a customer-specific table for Conversations
            await _tableServiceClient.UpsertEntityAsync(
                "TimeFiledEmails",
                filedEmail,
                customerDomain
            );
        }

        public async Task<Result<Conversation>> GetConversationAsync(
            string conversationId,
            string customerDomain,
            string projectId
        )
        {
            Result<Conversation> conversation =
                await _tableServiceClient.GetEntityAsync<Conversation>(
                    "Conversations",
                    conversationId,
                    projectId,
                    customerDomain
                );
            return conversation;
        }

        public async Task<Result<bool>> CheckConversationIsFiledAsync(
            string activeUser,
            string conversationId,
            string customerDomain
        )
        {
            string escapedEmail = activeUser.Replace("'", "''");

            string filter = $"PartitionKey eq '{escapedEmail}' and RowKey eq '{conversationId}'";
            List<Conversation> conversation =
                await _tableServiceClient.QueryEntitiesAsync<Conversation>(
                    "Conversations",
                    filter,
                    customerDomain
                );

            return (conversation.Count > 0);
        }

        public async Task<bool> IsEmailAlreadySavedAsync(
            string filingPath,
            string driveId,
            GraphServiceClient graphClient
        )
        {
            try
            {
                string normalizedPath = filingPath.Replace("\\", "/");

                var existingFile = await _graphApiClient.GetDriveItemByPathAsync(
                    driveId,
                    normalizedPath,
                    graphClient
                );
                return existingFile != null;
            }
            catch (Exception ex)
            {
                if (
                    ex is ServiceException serviceException
                    && serviceException.ResponseStatusCode == (int)HttpStatusCode.NotFound
                )
                {
                    return false;
                }
                return false;
            }
        }

        // public async Task<IEnumerable<Conversation>> GetConversationsFiledSinceLastAsync(
        //     string customerDomain,
        //     string projectCode,
        //     DateTime lastRetrievedTime
        // )
        // {
        //     string partitionKey = projectCode;
        //     string lastRetrievedPrefix = lastRetrievedTime.ToString(
        //         DateTimeFormat,
        //         CultureInfo.InvariantCulture
        //     );

        //     // Construct the filter to get conversations with RowKey greater than or equal to the last retrieval time
        //     string filter = $"PartitionKey eq '{partitionKey}'";

        //     // Query the Azure Table Storage using the filter in the customer-specific Conversations table
        //     var conversations = await _tableServiceClient.QueryEntitiesAsync<Conversation>(
        //         "Conversations",
        //         filter,
        //         customerDomain
        //     );

        //     return conversations;
        // }
    }
}
