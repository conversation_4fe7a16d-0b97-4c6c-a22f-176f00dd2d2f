namespace AtveroEmailFilingAzure.Services;

using AtveroEmailFiling.Models;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.EmailMetadata;
using AtveroEmailFiling.Services.EmailUploading;
using AtveroEmailFiling.Services.FilingStatusCheck;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.GraphApiService.Drives;
using AtveroEmailFiling.Services.GraphApiService.Emails;
using AtveroEmailFiling.Utils;
using FluentResults;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models.ExternalConnectors;
using Newtonsoft.Json;

public class FileOnSend
{
    private readonly ILogger _logger;
    private readonly IAzureTableService _tableService;
    private readonly IGraphDriveService _graphDriveService;
    private readonly IGraphApiClient _graphApiClient;
    private readonly IFilingStatusCheckService _filingStatusCheckService;
    private readonly IEmailUploadingService _emailUploadingService;
    private readonly IEmailMetadataService _emailMetadataService;

    private readonly IGraphEmailService _graphEmailService;

    private readonly ISyncTableService _syncService;

    public FileOnSend(
        ILogger logger,
        IAzureTableService tableService,
        IGraphDriveService graphDriveService,
        IGraphApiClient graphApiClient,
        IFilingStatusCheckService filingStatusCheckService,
        IEmailUploadingService emailUploadingService,
        IEmailMetadataService emailMetadataService,
        IGraphEmailService graphEmailService,
        ISyncTableService syncService
    )
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _tableService = tableService;
        _graphDriveService = graphDriveService;
        _graphApiClient = graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
        _filingStatusCheckService = filingStatusCheckService;
        _emailUploadingService = emailUploadingService;
        _emailMetadataService = emailMetadataService;
        _graphEmailService =
            graphEmailService ?? throw new ArgumentNullException(nameof(graphEmailService));
        _syncService = syncService ?? throw new ArgumentNullException(nameof(syncService));
    }

    public async Task<bool> ProcessFileOnSend(
        string userName,
        string customerDomainRaw,
        GraphServiceClient graphClient,
        DateTime? tokenExpiry,
        string activeUser,
        string[]? addinManifestIds
    )
    {
        // keep processing for 25 minutes
        // then stop so we release locks nicely

        System.DateTime timeoutTime = DateTime.UtcNow.Add(TimeSpan.FromMinutes(25));

        string customerDomain = customerDomainRaw;

        string? mappedDomain = await DomainUtils.MapDomain(customerDomainRaw, _tableService);
        if (mappedDomain != null)
        {
            _logger.LogInformation($"Changing to parent domain {mappedDomain}");
            customerDomain = mappedDomain;
        }

        Result<List<Microsoft.Graph.Models.MailFolder>> filingFoldersRes =
            await _graphEmailService.GetFilingFolders(graphClient, activeUser);

        if (filingFoldersRes.IsFailed)
        {
            _logger.LogInformation("Failed loading email folders");
            return false;
        }

        List<Microsoft.Graph.Models.MailFolder> userFolders = filingFoldersRes.Value;

        foreach (Microsoft.Graph.Models.MailFolder folder in userFolders)
        {
            // only work with Sent Items


            if (folder.DisplayName == AtveroEmailFilingAzure.Constants.SENT_ITEMS)
            {
                _logger.LogInformation("Processing mail folder " + folder.DisplayName);

                if (tokenExpiry <= DateTime.UtcNow)
                {
                    _logger.LogWarning("Token expired before processing. ");
                    return false;
                }

                // get a list of all messages added since the last run

                if (folder.Id != null)
                {
                    DateTime? lastSyncTimeResult = await _syncService.GetLastRetrievalTimeAsync(
                        userName + "-fos",
                        customerDomain
                    );

                    Result<List<Microsoft.Graph.Models.Message>> sentEmailsResult =
                        await _graphEmailService.GetSentItems(
                            graphClient,
                            folder.Id,
                            activeUser,
                            lastSyncTimeResult,
                            addinManifestIds
                        );

                    if (sentEmailsResult.IsSuccess)
                    {
                        _logger.LogInformation("Processing a set of sent emails");
                        List<Microsoft.Graph.Models.Message> receivedEmails =
                            sentEmailsResult.Value;

                        _logger.LogInformation(
                            "We have " + receivedEmails.Count + " sent items to process"
                        );

                        foreach (Microsoft.Graph.Models.Message email in receivedEmails.Take(1000)) // just in case we do something bad!
                        {
                            if (DateTime.UtcNow >= timeoutTime)
                            {
                                // run out of time
                                _logger.LogInformation("Ran out of time");
                                break;
                            }

                            try
                            {
                                _logger.LogInformation("Processing " + email.Id);
                                if (EmailUtils.AlreadyFiledCategories(email.Categories))
                                {
                                    _logger.LogInformation(
                                        "Skipping message as already marked as filed"
                                    );
                                    continue;
                                }

                                string? projectCode = null;
                                bool isImportant = false;
                                bool isConfidential = false;
                                string? filingTag = null;

                                // check the message metadata

                                if (email.SingleValueExtendedProperties != null)
                                {
                                    // String {00020329-0000-0000-c000-000000000046} Name cecp-e317c2f5-4cb6-437b-b305-30b84cb2ece0 - {"CmapMailFilingLocation":"MRMAIL"}

                                    foreach (var property in email.SingleValueExtendedProperties)
                                    {
                                        if (property.Value != null)
                                        {
                                            _logger.LogInformation(
                                                property.Id + " - " + property.Value
                                            );

                                            var metadata = JsonConvert.DeserializeObject<
                                                Dictionary<string, string>
                                            >(property.Value);
                                            if (metadata != null)
                                            {
                                                if (metadata.ContainsKey("CmapMailFilingLocation"))
                                                {
                                                    projectCode = metadata[
                                                        "CmapMailFilingLocation"
                                                    ];
                                                    _logger.LogInformation(
                                                        "Getting project code from metadata: "
                                                            + projectCode
                                                    );
                                                }

                                                if (metadata.ContainsKey("CmapMailFilingTag"))
                                                {
                                                    filingTag = metadata["CmapMailFilingTag"];
                                                    _logger.LogInformation(
                                                        "Getting filing tag from metadata: "
                                                            + filingTag
                                                    );
                                                }

                                                if (metadata.ContainsKey("CmapMailFilingImportant"))
                                                {
                                                    string isImportantStr = metadata[
                                                        "CmapMailFilingImportant"
                                                    ];
                                                    _logger.LogInformation(
                                                        "Getting isImportant from metadata: "
                                                            + isImportantStr
                                                    );

                                                    if (isImportantStr == "true")
                                                        isImportant = true;
                                                }

                                                if (
                                                    metadata.ContainsKey(
                                                        "CmapMailFilingConfidential"
                                                    )
                                                )
                                                {
                                                    string isConfidentialStr = metadata[
                                                        "CmapMailFilingConfidential"
                                                    ];
                                                    _logger.LogInformation(
                                                        "Getting isConfidential from metadata: "
                                                            + isConfidentialStr
                                                    );

                                                    if (isConfidentialStr == "true")
                                                        isConfidential = true;
                                                }
                                            }
                                        }
                                    }
                                }

                                if (
                                    projectCode == null
                                    && email.Subject != null
                                    && email.Subject.StartsWith('[')
                                )
                                {
                                    _logger.LogInformation("Subject starts with " + email.Subject);

                                    Int32 closePos = email.Subject.IndexOf(']');

                                    if (closePos > 1)
                                    {
                                        // autofile if this contains a project code that matches

                                        projectCode = email.Subject.Substring(1, closePos - 1);

                                        _logger.LogInformation(
                                            "Getting project code from subject: " + projectCode
                                        );
                                    }
                                }

                                if (projectCode != null)
                                {
                                    _logger.LogInformation("Filing to project code " + projectCode);

                                    Conversation conversationToApply = new Conversation()
                                    {
                                        ProjectCode = projectCode,
                                        RowKey = "xx", // will be ignored
                                        PartitionKey = "xx",
                                        Confidential = isConfidential,
                                        Important = isImportant,
                                    };

                                    if (filingTag != null)
                                    {
                                        conversationToApply.Tag = filingTag;
                                    }

                                    string? driveId = null;

                                    if (conversationToApply.SitePath != null)
                                    {
                                        _logger.LogInformation(
                                            "Using site Path from message to get drive ID "
                                                + conversationToApply.SitePath
                                        );
                                        Result<string> driveIdRes =
                                            await _graphDriveService.GetDriveIdFromSitePathAsync(
                                                conversationToApply.SitePath,
                                                conversationToApply.Confidential,
                                                graphClient
                                            );

                                        if (driveIdRes.IsSuccess)
                                        {
                                            driveId = driveIdRes.Value;
                                        }
                                    }
                                    else
                                    {
                                        // fall back to root site, can't process multi-geo

                                        Result<string> driveIdRes =
                                            await _graphDriveService.GetDriveIdFromProjectCodeAsync(
                                                conversationToApply.ProjectCode,
                                                conversationToApply.Confidential,
                                                graphClient
                                            );

                                        if (driveIdRes.IsSuccess)
                                        {
                                            driveId = driveIdRes.Value;
                                        }
                                    }

                                    if (driveId == null)
                                    {
                                        // can't get drive
                                        _logger.LogWarning(
                                            $"Unable to locate project drive for {conversationToApply.ProjectCode}"
                                        );
                                        throw new Exception(
                                            $"Unable to access destination project {conversationToApply.ProjectCode}"
                                        );
                                    }

                                    if (email.Id != null)
                                    {
                                        AtveroEmailFiling.Models.OutlookMailMessage? emailToFile =
                                            EmailUtils.CreateOutlookMailMessage(email);

                                        string filingPath =
                                            EmailUtils.GenerateFilingPathCheckSubject(
                                                emailToFile,
                                                conversationToApply.Tag
                                            );

                                        bool isEmailAlreadySaved =
                                            await _filingStatusCheckService.IsEmailAlreadySavedAsync(
                                                filingPath,
                                                driveId,
                                                graphClient
                                            );

                                        if (isEmailAlreadySaved)
                                        {
                                            await _emailMetadataService.SetEmailMetadata(
                                                email.Id,
                                                conversationToApply.ProjectCode,
                                                graphClient,
                                                conversationToApply.Tag,
                                                conversationToApply.Important,
                                                conversationToApply.Confidential,
                                                AtveroEmailFilingAzure.Constants.FILED_TAG,
                                                userName,
                                                activeUser
                                            );
                                        }
                                        else
                                        {
                                            _logger.LogInformation("We have a sent item to file");

                                            List<(
                                                OutlookMailMessage msg,
                                                string filingPath,
                                                string messageId
                                            )> messagesToFile =
                                            [
                                                (emailToFile, filingPath, email.Id),
                                            ];

                                            MetaData metadata = new MetaData()
                                            {
                                                Confidential = conversationToApply.Confidential,
                                                Important = conversationToApply.Important,
                                                Tag = conversationToApply.Tag,
                                                ProjectCode = conversationToApply.ProjectCode,
                                            };

                                            Result uploadedAll =
                                                await _emailUploadingService.ProcessMessagesAsync(
                                                    messagesToFile,
                                                    driveId,
                                                    graphClient,
                                                    metadata,
                                                    _emailMetadataService,
                                                    userName,
                                                    customerDomain,
                                                    AtveroEmailFilingAzure.Constants.FILED_TAG,
                                                    activeUser,
                                                    userName,
                                                    null
                                                );

                                            if (uploadedAll.IsSuccess)
                                            {
                                                await StatsUtils.LogMessageFiledOnSend(
                                                    _tableService,
                                                    customerDomain,
                                                    activeUser,
                                                    1,
                                                    _logger
                                                );
                                            }

                                            // also add this conversation to the list

                                            if (email.ConversationId != null)
                                            {
                                                _logger.LogInformation(
                                                    "Adding the sent item conversation to the filing list"
                                                );
                                                await EmailUtils.MarkConversationForFiling(
                                                    _filingStatusCheckService,
                                                    userName,
                                                    email.ConversationId,
                                                    conversationToApply.ProjectCode,
                                                    conversationToApply.Tag,
                                                    conversationToApply.Confidential,
                                                    conversationToApply.Important,
                                                    activeUser,
                                                    customerDomain
                                                );
                                            }
                                            else
                                            {
                                                _logger.LogInformation(
                                                    "Message doesn't have a conversation ID"
                                                );
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    _logger.LogInformation("Email not tagged, so not for filing");
                                }
                            }
                            catch (Exception ex)
                            {
                                // this can happen when future filing runs before messages are filed on first run
                                _logger.LogInformation(
                                    "Failed checking for file on send " + ex.Message
                                );
                            }
                        }
                    }
                    else
                    {
                        _logger.LogInformation("Failed to get emails for folder");
                    }
                }
                else
                {
                    _logger.LogInformation("Fetched folder had a null ID");
                }
            }
        }

        return true;
    }
}
