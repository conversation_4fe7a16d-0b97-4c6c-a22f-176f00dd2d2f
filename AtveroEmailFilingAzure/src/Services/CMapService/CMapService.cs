﻿using AtveroEmailFiling.Models.CmapProjects;
using FluentResults;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Services.CMapService
{
    public class CMapService : ICMapService
    {
        private readonly ILogger _logger;
        private readonly string _apiKey;

        public CMapService(string apiKey, ILogger logger)
        {
            _apiKey = apiKey;
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        // Method to get projects using token
        public async Task<Result<List<Project>>> GetProjectsAsync(HttpClient client, string? search)
        {
            try
            {
                _logger.LogInformation("Starting request to retrieve projects.");

                var request = new HttpRequestMessage(
                    HttpMethod.Get,
                    "https://api.cmaphq.com/v1/Projects?status=project&q=" + search
                );
                request.Headers.Add("Content-Api-Key", _apiKey);

                var response = await client.SendAsync(request);
                var responseBody = await response.Content.ReadAsStringAsync();
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError(
                        $"Failed to retrieve projects with status code: {response.StatusCode}"
                    );
                    return Result.Fail(
                        new Error("CMap project call failed").CausedBy(responseBody)
                    );
                }

                var projects = JsonConvert.DeserializeObject<List<Project>>(responseBody);

                _logger.LogInformation("Successfully retrieved projects.");
                if (null == projects)
                {
                    return Result.Ok(new List<Project>());
                }
                return Result.Ok(projects);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"An error occurred while retrieving projects");
                return Result.Fail(
                    new Error("An error occured while getting projects from CMap").CausedBy(ex)
                );
            }
        }
    }
}
