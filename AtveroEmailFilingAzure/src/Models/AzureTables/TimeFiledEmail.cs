using Azure;
using Azure.Data.Tables;
using Microsoft.Kiota.Abstractions;

namespace AtveroEmailFiling.Models.AzureTables
{
    public class TimeFiledEmail : ITableEntity
    {
        public TimeFiledEmail() { }

        public required string PartitionKey { get; set; }
        public required string RowKey { get; set; }

        public DateTimeOffset? Timestamp { get; set; }
        public ETag ETag { get; set; }

        public string? SitePath { get; set; }
    }
}
