using System.Globalization;
using Azure;
using Azure.Data.Tables;
using Microsoft.Graph.Models.Security;

namespace AtveroEmailFiling.Models.AzureTables
{
    public class Conversation : ITableEntity
    {
        public Conversation() { }

        public required string ProjectCode { get; set; }
        public string? SitePath { get; set; }

        public required string PartitionKey { get; set; }
        public required string RowKey { get; set; }

        public string? Tag { get; set; }

        public bool Confidential { get; set; }
        public bool Important { get; set; }

        public DateTimeOffset? Timestamp { get; set; }
        public ETag ETag { get; set; }
    }
}
