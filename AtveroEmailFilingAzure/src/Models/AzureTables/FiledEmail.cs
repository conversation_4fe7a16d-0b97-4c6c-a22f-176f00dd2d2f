using Azure;
using Azure.Data.Tables;

namespace AtveroEmailFiling.Models.AzureTables
{
    public class FiledEmail : ITableEntity
    {
        public FiledEmail() { }

        public static string generateRowKey()
        {
            return string.Format("{0:D19}", DateTime.MaxValue.Ticks - DateTime.UtcNow.Ticks);
        }

        public required string ProjectCode { get; set; }

        public string? SitePath { get; set; }

        public required string PartitionKey { get; set; }
        public required string RowKey { get; set; }
        public string? Tag { get; set; }
        public bool Confidential { get; set; }

        public bool Important { get; set; }

        public required string FiledBy { get; set; }

        public required string FiledByEmail { get; set; }

        public DateTimeOffset? Timestamp { get; set; }
        public ETag ETag { get; set; }
    }
}
