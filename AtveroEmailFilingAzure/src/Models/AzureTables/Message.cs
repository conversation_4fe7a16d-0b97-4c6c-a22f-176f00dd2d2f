using Azure;
using Azure.Data.Tables;
using Microsoft.Graph.Models;

namespace AtveroEmailFiling.Models.AzureTables
{
    public enum JobStatus
    {
        Pending,
        Completed,
        Failed,
    }

    public class Message : ITableEntity
    {
        public Message() { }

        public required string CustomerDomain { get; set; }
        public required string MessageId { get; set; }

        public string? SitePath { get; set; }

        public string? PersistentMessageId { get; set; }

        public string? SharedMailbox { get; set; }

        public required string ProjectCode { get; set; }
        public string? Tag { get; set; }

        public string? UserId { get; set; }

        public bool Important { get; set; }
        public bool Confidential { get; set; }

        public int DequeueCount { get; set; }

        public string? FailureReason { get; set; }

        public JobStatus Status { get; set; }

        // Azure-specific fields for partitioning and row uniqueness.
        public required string PartitionKey { get; set; } // ProjectCode
        public required string RowKey { get; set; } // GUID
        public DateTimeOffset? Timestamp { get; set; }
        public ETag ETag { get; set; }
    }
}
