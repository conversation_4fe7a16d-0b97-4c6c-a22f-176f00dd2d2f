namespace AtveroEmailFiling.Models.ApiResponses
{
    public class ApiResponse<T>
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public T? Data { get; set; }

        public ApiResponse()
        {
            Message = "Error - Not Set";
        }

        // Constructor with all parameters
        public ApiResponse(bool isSuccess, string message, T data)
        {
            IsSuccess = isSuccess;
            Message = message;
            Data = data;
        }

        // // Constructor without the 'data' parameter
        public ApiResponse(bool isSuccess, string message)
        {
            IsSuccess = isSuccess;
            Message = message;
        }
        // : this(isSuccess, message, default) { }
    }
}
