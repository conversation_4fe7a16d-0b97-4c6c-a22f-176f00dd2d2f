using Newtonsoft.Json;

namespace AtveroEmailFiling.Models.ApiRequests
{
    public class SubscriptionRequestItem
    {
        [JsonProperty("subscriptionId")]
        public string? SubscriptionId { get; set; }

        [JsonProperty("subscriptionExpirationDateTime")]
        public DateTime? SubscriptionExpirationDateTime { get; set; }

        [JsonProperty("changeType")]
        public string? ChangeType { get; set; }

        [JsonProperty("resource")]
        public string? Resource { get; set; }

        [JsonProperty("clientState")]
        public string? clientState { get; set; }

        [JsonProperty("tenantId")]
        public string? TenantID { get; set; }
    }

    public class SubscriptionRequest
    {
        [JsonProperty("value")]
        public List<SubscriptionRequestItem>? request { get; set; }
    }
}
