﻿using System.Text.Json.Serialization;

namespace AtveroEmailFiling.Models.ApiRequests
{
    /// <summary>
    /// Represents a request containing a list of favourite items.
    /// </summary>
    public class FavouritesRequest
    {
        /// <summary>
        /// Gets or sets the list of favourite items.
        /// </summary>
        public List<Favourite>? Favourites { get; set; }
    }

    /// <summary>
    /// Represents a favourite item with project details.
    /// </summary>
    public class Favourite
    {
        /// <summary>
        /// Gets or sets the project code of the favourite item.
        /// </summary>
        [JsonPropertyName("ProjectCode")]
        public string? ProjectCode { get; set; }

        /// <summary>
        /// Gets or sets the project title of the favourite item.
        /// </summary>
        [JsonPropertyName("ProjectTitle")]
        public string? ProjectTitle { get; set; }

        [JsonPropertyName("SitePath")]
        public string? SitePath { get; set; }
    }
}
