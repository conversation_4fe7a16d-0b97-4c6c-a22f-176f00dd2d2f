using Newtonsoft.Json;

namespace AtveroEmailFiling.Models.ApiRequests
{
    public class FileConversationRequest
    {
        [JsonProperty("hubSite")]
        public string? HubSite { get; set; }

        [JsonProperty("sharedMailbox")]
        public string? SharedMailbox { get; set; }

        [JsonProperty("conversationId")]
        public string? ConversationId { get; set; }

        [Json<PERSON>roperty("projectCode")]
        public string? ProjectCode { get; set; }

        [JsonProperty("tag")]
        public string? Tag { get; set; }

        [JsonProperty("emails")]
        public List<Email>? Emails { get; set; }

        [JsonProperty("isConfidential")]
        public bool Confidential { get; set; }

        [JsonProperty("isImportant")]
        public bool Important { get; set; }
    }
}
