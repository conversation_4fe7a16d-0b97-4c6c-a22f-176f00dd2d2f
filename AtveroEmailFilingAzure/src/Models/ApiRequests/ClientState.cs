using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFilingAzure.Models.ApiRequests;

public class ClientState
{
    public required string SharedSecret { get; set; }
    public string? ClientId { get; set; }

    public static ClientState Parse(
        ILogger logger,
        string clientState,
        IConfiguration configuration
    )
    {
        try
        {
            ClientState? state = JsonConvert.DeserializeObject<ClientState>(clientState);
            if (null == state)
            {
                logger.LogInformation(
                    "Failed to parse client state, using client state as secret with the default client id"
                );
                new ClientState()
                {
                    SharedSecret = clientState,
                    ClientId = configuration["DefaultApiClientId"],
                };
            }
            return state;
        }
        catch (Exception)
        {
            logger.LogInformation(
                "Exception parsing client state, using client state as secret with the default client id"
            );
            return new ClientState()
            {
                SharedSecret = clientState,
                ClientId = configuration["DefaultApiClientId"],
            };
        }
    }
}
