using Newtonsoft.Json;

namespace AtveroEmailFiling.Models.QueueMessages
{
    public class FiledByOthersQueueMessage
    {
        [JsonProperty("token")]
        public string? Token { get; set; }

        [JsonProperty("userId")]
        public string? UserId { get; set; }

        [JsonProperty("customerDomain")] // Adding customerDomain for multi-tenancy context
        public string? CustomerDomain { get; set; }

        [JsonProperty("sharedMailbox")]
        public string? SharedMailbox { get; set; }
    }
}
