using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.CsomClientService;
using AtveroEmailFiling.Services.EmailMetadata;
using AtveroEmailFiling.Services.EmailRetrieval;
using AtveroEmailFiling.Services.EmailUploading;
using AtveroEmailFiling.Services.FavService;
using AtveroEmailFiling.Services.FilingStatusCheck;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.GraphApiService.Drives;
using AtveroEmailFiling.Services.GraphApiService.Emails;
using AtveroEmailFiling.Services.GraphApiService.Sites;
using AtveroEmailFiling.Services.MSCsomClientContextService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using AtveroEmailFilingAzure.src.Services.SharepointApi;
using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using RedLockNet;
using RedLockNet.SERedis;
using RedLockNet.SERedis.Configuration;
using Sentry.Azure.Functions.Worker;
using StackExchange.Redis;

var host = new HostBuilder()
    .ConfigureFunctionsWebApplication()
    .ConfigureServices(services =>
    {
        // Application Insights setup
        // services.AddApplicationInsightsTelemetryWorkerService();
        // services.ConfigureFunctionsApplicationInsights();

        services.AddHttpClient();
        services.AddScoped<ITokenValidationService, TokenValidationService>();
        services.AddSingleton<IGraphClientService, GraphClientService>();
        services.AddScoped<IGraphSiteService, GraphSiteService>();

        var connectionString = Environment.GetEnvironmentVariable("AzureWebJobsStorage");
        var redisConnectionString = Environment.GetEnvironmentVariable(
            "AZURE_REDIS_CONNECTIONSTRING"
        );

        List<RedLockMultiplexer> multiplexer = new List<RedLockMultiplexer>
        {
            ConnectionMultiplexer.Connect(redisConnectionString),
        };

        var redlockFactory = RedLockFactory.Create(multiplexer);
        services.AddSingleton<IDistributedLockFactory>(redlockFactory);

        // // Table Storage Service
        string? tableStorageUri = Environment.GetEnvironmentVariable("TableStorageUri");
        string? accountName = Environment.GetEnvironmentVariable("StorageAccountName");
        string? accountKey = Environment.GetEnvironmentVariable("StorageAccountKey");
        // Register IAzureTableService with DI

        if (tableStorageUri != null && accountName != null && accountKey != null)
            services.AddSingleton<IAzureTableService>(sp =>
            {
                var logger = sp.GetRequiredService<ILogger<AzureTableServiceClient>>();
                return new AzureTableServiceClient(
                    tableStorageUri,
                    accountName,
                    accountKey,
                    logger
                );
            });

        // Graph API Client and related services
        services.AddScoped<IGraphApiClient, GraphApiClient>();
        services.AddSingleton<IGraphEmailService, GraphEmailService>();
        services.AddSingleton<ICsomClientContextService, CsomClientContextService>();
        services.AddSingleton<ISharepointApiFactory, SharepointApiFactory>();
        services.AddScoped<IEmailRetrievalService, EmailRetrievalService>();

        // services.AddScoped<ICustomerOnboardingService, CustomerOnboardingService>();

        services.AddTransient<IEmailMetadataService, EmailMetadataService>();
        services.AddTransient<IEmailUploadingService, EmailUploadingService>();
        services.AddTransient<IFilingStatusCheckService, FilingStatusCheckService>();
        services.AddScoped<IGraphDriveService, GraphDriveService>();
        services.AddScoped<IFavouritesService, FavouritesService>();
        services.AddScoped<ICsomApiClient, CsomApiClient>();

        // Sync Table Service
        services.AddScoped<ISyncTableService, SyncTableService>();
    })
    .ConfigureAppConfiguration(
        (_ctx, builder) =>
        {
            builder.AddEnvironmentVariables();
            try
            {
                var tmpConfig = builder.Build();
                var vaultUri = tmpConfig["CMAP_API_VAULT_URL"];
                if (string.IsNullOrWhiteSpace(vaultUri))
                {
                    throw new ArgumentException("please provide a valid VaultUri");
                }

                builder.AddAzureKeyVault(
                    new Uri(vaultUri),
                    new Azure.Identity.DefaultAzureCredential(),
                    new AzureKeyVaultConfigurationOptions
                    {
                        ReloadInterval = TimeSpan.FromMinutes(5),
                    }
                );
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }
    )
    .ConfigureLogging(logging =>
    {
        logging.Services.Configure<LoggerFilterOptions>(options =>
        {
            var defaultRule = options.Rules.FirstOrDefault(rule =>
                rule.ProviderName
                == "Microsoft.Extensions.Logging.ApplicationInsights.ApplicationInsightsLoggerProvider"
            );
            if (defaultRule is not null)
            {
                options.Rules.Remove(defaultRule);
            }
        });
    })
    .ConfigureFunctionsWorkerDefaults(
        (host, builder) =>
        {
            builder.UseSentry(
                host,
                options =>
                {
                    options.Dsn =
                        "https://<EMAIL>/4508551326203904";
                    // When configuring for the first time, to see what the SDK is doing:
                    options.Debug = false;
                    // Set traces_sample_rate to 1.0 to capture 100% of transactions for performance monitoring.
                    // We recommend adjusting this value in production.
                    options.TracesSampleRate = 1.0;
                    options.Release =
                        $"{EmailUtils.GetServiceName()}@{EmailUtils.GetReleaseVersion()}";
                }
            );
        }
    )
    .Build();

await host.RunAsync();
