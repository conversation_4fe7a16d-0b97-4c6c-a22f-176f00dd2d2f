using CMapPim.Model;
using FluentResults;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Newtonsoft.Json;

namespace CMapPim.Utils
{
    public class GraphUtils
    {
        public static List<string> getLookupIdsFromMultiLookupField(string multiLookupResult)
        {
            List<String>? returnList = null;
            // Newtonsoft.Json.Linq.JArray? rawResults = multiLookupResult as Newtonsoft.Json.Linq.JArray;
            // if (rawResults != null)
            // {
            //   foreach (Newtonsoft.Json.Linq.JToken rawResult in rawResults)
            //   {
            //     Newtonsoft.Json.Linq.JObject? lookupIdObj = rawResult as Newtonsoft.Json.Linq.JObject;
            //     if (lookupIdObj != null)
            //     {
            //       string? lookupId = lookupIdObj.GetValue("LookupValue")?.ToString();
            //       if (lookupId != null)
            //         returnList.Add(lookupId);
            //     }
            //   }
            // }

            // try {
            //   returnList = JsonConvert<List<string>>.DeserializeObject(multiLookupResult)
            // } catch(Exception e) {
            //   // Can't parse try the other method
            // }

            if (null == returnList)
            {
                if (Int32.TryParse(multiLookupResult, out Int32 number))
                {
                    return new List<string> { multiLookupResult };
                }
                returnList = new List<string>();
                List<Lookup>? lookups = JsonConvert.DeserializeObject<List<Lookup>>(
                    multiLookupResult
                );
                if (lookups != null)
                {
                    foreach (Lookup lookup in lookups)
                    {
                        if (lookup.LookupValue != null)
                            returnList.Add(lookup.LookupValue);
                    }
                }
            }
            return returnList;
        }

        public static List<Int32> GetLookupIdsFromMultiLookupFieldAsInt(string multiLookupResult)
        {
            List<string> stringIds = getLookupIdsFromMultiLookupField(multiLookupResult);

            return stringIds.Select(i => Int32.Parse(i)).ToList();
        }

        public static Stream CopyStream(Stream inputStream)
        {
            MemoryStream ms = new MemoryStream();

            inputStream.Seek(0, 0);
            inputStream.CopyTo(ms);
            inputStream.Seek(0, 0);

            ms.Seek(0, 0);

            return ms;
        }

        public static string CleanGuid(string guid)
        {
            string cleanedGuid = Guid.Parse(guid as string).ToString();
            return cleanedGuid;
        }

        public static Result<string> GetSharePointSubdomain(string webUrl)
        {
            Uri webUri = new Uri(webUrl);
            string[] parts = webUri.Host.Split(".");
            if (0 == parts.Length)
            {
                return Result.Fail("Can't split domain for sptenant " + webUrl);
            }

            return Result.Ok(parts[0]);
        }
    }
}
