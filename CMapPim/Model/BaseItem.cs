using System.Collections;
using CMapPim.Utils;
using FluentResults;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;

namespace CMapPim.Model
{
    public class BaseItem
    {
        private Hashtable stringAttributes;
        private Hashtable intAttributes;

        private Hashtable multiLookps;

        private Hashtable singleLookups;

        private readonly Hashtable boolAttributes;

        private readonly Hashtable dateTimeAttributes;

        public string[] stringAttributeNames;
        public string[] intAttributeNames;
        public string[] multiLookupNames;

        public string[] boolAttributeNames;

        public string[] dateTimeAttributeNames;

        public string[] singleLookupNames;

        public string tableName { get; }

        protected ILogger logger;

        protected GraphListItem? listItem;

        public string? GUID
        {
            get
            {
                if (listItem == null)
                    return null;
                Result<string> guid = listItem.GetStringValue("GUID");
                if (guid.IsSuccess)
                {
                    return GraphUtils.CleanGuid(guid.ValueOrDefault);
                }
                return null;
            }
        }

        public BaseItem(
            string tableName,
            string[] stringAttributeNames,
            string[] intAttributeNames,
            string[] boolAttributeNames,
            string[] dateTimeAttributeNames,
            string[] multiLookupNames,
            string[] singleLookupNames,
            ILogger logger,
            GraphListItem listItem
        )
        {
            this.stringAttributes = new Hashtable();
            this.intAttributes = new Hashtable();
            this.multiLookps = new Hashtable();
            this.singleLookups = new Hashtable();
            this.boolAttributes = new Hashtable();
            this.dateTimeAttributes = new Hashtable();

            this.stringAttributeNames = stringAttributeNames;
            this.intAttributeNames = intAttributeNames;
            this.multiLookupNames = multiLookupNames;
            this.singleLookupNames = singleLookupNames;
            this.boolAttributeNames = boolAttributeNames;
            this.dateTimeAttributeNames = dateTimeAttributeNames;
            this.logger = logger;

            this.tableName = tableName;
            this.listItem = listItem;
        }

        public BaseItem(
            string tableName,
            string[] stringAttributeNames,
            string[] intAttributeNames,
            string[] boolAttributeNames,
            string[] dateTimeAttributeNames,
            string[] multiLookupNames,
            string[] singleLookupNames,
            ILogger? logger
        )
        {
            this.stringAttributes = new Hashtable();
            this.intAttributes = new Hashtable();
            this.multiLookps = new Hashtable();
            this.singleLookups = new Hashtable();
            this.boolAttributes = new Hashtable();
            this.dateTimeAttributes = new Hashtable();

            this.stringAttributeNames = stringAttributeNames;
            this.intAttributeNames = intAttributeNames;
            this.multiLookupNames = multiLookupNames;
            this.singleLookupNames = singleLookupNames;
            this.boolAttributeNames = boolAttributeNames;
            this.dateTimeAttributeNames = dateTimeAttributeNames;
            this.tableName = tableName;
            this.listItem = null;
            this.logger = logger;
        }

        public int GetId()
        {
            if (this.listItem != null)
            {
                return listItem.GetId();
            }
            else
                return 0;
        }

        public void SetListItem(GraphListItem item)
        {
            this.listItem = item;
        }

        protected void CopyValues(GraphListItem listItem)
        {
            foreach (string fieldName in stringAttributeNames)
            {
                Result<string> valueRes = listItem.GetStringValue(fieldName);
                if (valueRes.IsSuccess)
                {
                    SetFieldAsString(fieldName, valueRes.Value);
                }
            }

            foreach (string fieldName in intAttributeNames)
            {
                Result<int> valueRes = listItem.GetIntegerValue(fieldName);
                if (valueRes.IsSuccess)
                {
                    SetFieldAsInteger(fieldName, valueRes.Value);
                }
            }

            foreach (string fieldName in multiLookupNames)
            {
                Result<List<int>> existing = listItem.GetMultiLookupValues(fieldName);

                if (existing.IsSuccess)
                {
                    List<int> val = existing.Value;
                    SetFieldAsMultiLookup(fieldName, val);
                }
            }

            foreach (string fieldName in singleLookupNames)
            {
                Result<int> existing = listItem.GetSingleLookupValue(fieldName);

                if (existing.IsSuccess)
                {
                    int val = existing.Value;
                    SetFieldAsSingleLookup(fieldName, val);
                }
            }

            foreach (string fieldName in boolAttributeNames)
            {
                Result<bool> valueRes = listItem.GetBoolValue(fieldName);
                if (valueRes.IsSuccess)
                {
                    SetFieldAsBool(fieldName, valueRes.Value);
                }
            }
            foreach (string fieldName in dateTimeAttributeNames)
            {
                Result<DateTime?> valueRes = listItem.GetDateTimeValue(fieldName);
                if (valueRes.IsSuccess)
                {
                    SetFieldAsDateTime(fieldName, valueRes.Value);
                }
            }
        }

        public string GetFieldAsString(string fieldName)
        {
            if (stringAttributeNames.Contains(fieldName))
                if (stringAttributes.ContainsKey(fieldName))
                {
                    string? value = (string?)stringAttributes[fieldName];
                    if (value != null)
                        return value;
                    else
                        return "";
                }
                else
                    return "";
            else
                throw new ArgumentException("Non a valid attribute", fieldName);
        }

        protected void SetFieldAsString(string fieldName, string? value)
        {
            if (stringAttributes.ContainsKey(fieldName))
            {
                stringAttributes.Remove(fieldName);
            }
            stringAttributes[fieldName] = value;
        }

        protected void SetNameFieldAsString(string fieldName, string? value)
        {
            List<string> tempAttributeNames = new List<string>(stringAttributeNames);
            if (!stringAttributeNames.Contains(fieldName))
            {
                tempAttributeNames.Add(fieldName);
            }

            stringAttributeNames = tempAttributeNames.ToArray();
            stringAttributes[fieldName] = value;
        }

        public int GetFieldAsInteger(string fieldName)
        {
            if (intAttributeNames.Contains(fieldName))
            {
                if (intAttributes.ContainsKey(fieldName))
                {
                    object? value = intAttributes[fieldName];

                    ////
                    ///

                    if (value != null)
                    {
                        if (Single.TryParse(value.ToString(), out Single ret))
                        {
                            // string svalue = ret.ToString("0");
                            // return Int32.Parse(svalue);
                            return (int)ret;
                        }
                    }

                    if (value != null)
                        return (int)value;
                    else
                    {
                        return 0;
                    }
                }
                else
                {
                    return 0;
                }
            }
            else
                throw new ArgumentException("Non a valid attribute", fieldName);
        }

        public List<int> GetFieldAsMultiLookup(string fieldName)
        {
            if (multiLookupNames.Contains(fieldName))
                if (multiLookps.ContainsKey(fieldName))
                {
                    object? value = multiLookps[fieldName];

                    if (value != null)
                        return (List<int>)value;
                    else
                    {
                        return new List<int>();
                    }
                }
                else
                    return new List<int>();
            else
                throw new ArgumentException("Non a valid attribute", fieldName);
        }

        public int GetFieldAsSingleLookup(string fieldName)
        {
            if (singleLookupNames.Contains(fieldName))
                if (singleLookups.ContainsKey(fieldName))
                {
                    object? value = singleLookups[fieldName];

                    if (value != null)
                        return (int)value;
                    else
                    {
                        return 0;
                    }
                }
                else
                    return 0;
            else
                throw new ArgumentException("Non a valid attribute", fieldName);
        }

        protected void SetFieldAsInteger(string fieldName, int? value)
        {
            if (intAttributes.ContainsKey(fieldName))
            {
                intAttributes.Remove(fieldName);
            }
            intAttributes[fieldName] = value;
        }

        protected void SetFieldAsMultiLookup(string fieldName, List<int> value)
        {
            if (multiLookps.ContainsKey(fieldName))
            {
                multiLookps.Remove(fieldName);
            }
            multiLookps[fieldName] = value;
        }

        protected void SetFieldAsSingleLookup(string fieldName, int? value)
        {
            if (singleLookups.ContainsKey(fieldName))
            {
                singleLookups.Remove(fieldName);
            }
            singleLookups[fieldName] = value;
        }

        public bool GetFieldAsBool(string fieldName)
        {
            if (boolAttributeNames.Contains(fieldName))
            {
                if (boolAttributes.ContainsKey(fieldName))
                {
                    object? value = boolAttributes[fieldName];

                    if (value != null)
                        return (bool)value;
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                throw new ArgumentException("Non a valid attribute", fieldName);
            }
        }

        protected void SetFieldAsBool(string fieldName, bool value)
        {
            if (boolAttributes.ContainsKey(fieldName))
            {
                boolAttributes.Remove(fieldName);
            }
            boolAttributes[fieldName] = value;
        }

        public DateTime? GetFieldAsDateTime(string fieldName)
        {
            if (dateTimeAttributeNames.Contains(fieldName))
            {
                if (dateTimeAttributes.ContainsKey(fieldName))
                {
                    object? value = dateTimeAttributes[fieldName];

                    if (value != null)
                        return (DateTime)value;
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            else
            {
                throw new ArgumentException("Non a valid attribute", fieldName);
            }
        }

        protected void SetFieldAsDateTime(string fieldName, DateTime? value)
        {
            if (dateTimeAttributes.ContainsKey(fieldName))
            {
                dateTimeAttributes.Remove(fieldName);
            }
            dateTimeAttributes[fieldName] = value;
        }

        // public async Task<Result> Update(Context ctx)
        // {
        //     Result<GraphListItem> limRes = await ctx.GraphApiClientUpdate(this);
        //     if (limRes.IsSuccess)
        //     {
        //         return Result.Ok();
        //     }
        //     else
        //     {
        //         return limRes.ToResult();
        //     }
        // }

        public ListItem PrepareListItem()
        {
            ListItem item = new ListItem();

            Dictionary<string, object> newItemData = new Dictionary<string, object>();

            foreach (string fieldName in stringAttributeNames)
            {
                string value = GetFieldAsString(fieldName);
                if (value != "")
                {
                    newItemData.Add(fieldName, value);
                }
            }

            foreach (string fieldName in intAttributeNames)
            {
                int value = GetFieldAsInteger(fieldName);
                if (value != 0)
                    newItemData.Add(fieldName, value);
            }

            foreach (string fieldName in dateTimeAttributeNames)
            {
                DateTime? value = GetFieldAsDateTime(fieldName);
                if (value.HasValue)
                    newItemData.Add(fieldName, value.Value);
            }

            foreach (string fieldName in multiLookupNames)
            {
                List<int> vals = GetFieldAsMultiLookup(fieldName);
                if (vals != null && vals.Count > 0)
                {
                    newItemData.Add(fieldName + "<EMAIL>", "Collection(Edm.Int32)");
                    newItemData.Add(fieldName + "LookupId", vals);
                }
            }

            foreach (string fieldName in singleLookupNames)
            {
                int val = GetFieldAsSingleLookup(fieldName);
                if (val > 0)
                {
                    string stringVal = val.ToString();
                    newItemData.Add(fieldName, stringVal);
                }
            }
            foreach (string fieldName in boolAttributeNames)
            {
                bool value = GetFieldAsBool(fieldName);
                newItemData.Add(fieldName, value);
            }

            ListItem newItem = new ListItem
            {
                Fields = new FieldValueSet { AdditionalData = newItemData },
            };
            return newItem;
        }
    }
}
