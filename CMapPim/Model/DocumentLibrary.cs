using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AtveroEmailFiling.Services.GraphApiService;
using FluentResults;
using Microsoft.Graph;
using Microsoft.Graph.Models;

namespace CMapPim.Model;

public class DocumentLibrary
{
    public string Name
    {
        get { return libraryName; }
    }
    private string libraryName;
    private string id;

    public DocumentLibrary(string libraryName)
    {
        this.libraryName = libraryName;
    }

    public async Task<Result<string>> GetId(Context ctx)
    {
        if (string.IsNullOrEmpty(id))
        {
            Result<Site> site = await ctx.GraphApiClient.GetSiteAsync(ctx.SiteUrl, ctx.GraphClient);
            if (site.IsFailed)
            {
                return site.ToResult();
            }

            Result<DriveCollectionResponse?> drives = await ctx.GraphApiClient.GetSiteDrivesAsync(
                site.Value.Id,
                ctx.GraphClient
            );
            if (drives.IsSuccess)
            {
                string? id = drives
                    .Value?.Value?.FirstOrDefault(d =>
                        d.Name != null
                        && d.Name.Equals(libraryName, StringComparison.OrdinalIgnoreCase)
                    )
                    ?.Id;
                if (id == null)
                {
                    return Result.Fail("Drive not found");
                }
                else
                {
                    this.id = id;
                }
                Console.WriteLine($"Drive ID is {id}");
            }
            else
            {
                return drives.ToResult();
            }
        }
        return Result.Ok(id);
    }
}
