using CMapPim.Utils;
using FluentResults;
using Microsoft.Graph.Models;
using Microsoft.Kiota.Abstractions.Serialization;

namespace CMapPim.Model
{
    public class GraphListItem
    {
        public ListItem listItem; // make private again

        public GraphListItem(ListItem listItem)
        {
            this.listItem = listItem;
        }

        public int GetId()
        {
            return (int)Int32.Parse(listItem.Id);
        }

        private Result<ActiveUser> GraphUserToActiveUser(User user)
        {
            return Result.Ok(
                new ActiveUser()
                {
                    FullName = user.DisplayName,
                    FirstName = StringUtils.FirstNameFromFullname(user.DisplayName),
                    LastName = StringUtils.LastNameFromFullname(user.DisplayName),
                    Email = user.Mail,
                    Id = user.Id,
                    JobTitle = user.JobTitle,
                    Mobile = user.MobilePhone,
                    OfficeTelephone = string.Join(",", user.BusinessPhones),
                }
            );
        }

        public Result<ActiveUser> GetCreator()
        {
            User created = listItem.CreatedByUser;
            if (null == created)
            {
                ActiveUser activeUser = new ActiveUser()
                {
                    FullName = listItem.CreatedBy.User.DisplayName,
                };

                if (listItem.CreatedBy.User.AdditionalData.ContainsKey("email"))
                {
                    activeUser.Email = listItem.CreatedBy.User.AdditionalData["email"]?.ToString();
                }

                return Result.Ok(activeUser);
            }
            else
            {
                return GraphUserToActiveUser(created);
            }
        }

        public Result<string> GetStringValue(string fieldName)
        {
            if (
                listItem != null
                && listItem.Fields != null
                && listItem.Fields.AdditionalData != null
            )
            {
                if (listItem.Fields.AdditionalData.ContainsKey(fieldName))
                {
                    string? value = listItem.Fields.AdditionalData[fieldName].ToString();
                    if (value != null)
                    {
                        return Result.Ok(value);
                    }
                }

                return Result.Fail("Value not set");
            }
            else
            {
                return Result.Fail("No item fetched");
            }
        }

        public Result<bool> GetBoolValue(string fieldName)
        {
            if (null != listItem?.Fields?.AdditionalData)
            {
                if (listItem.Fields.AdditionalData.ContainsKey(fieldName))
                {
                    string? value = listItem.Fields?.AdditionalData[fieldName]?.ToString();
                    if (value != null)
                    {
                        try
                        {
                            bool actualValue = bool.Parse(value);
                            return Result.Ok(actualValue);
                        }
                        catch (Exception e)
                        {
                            return ResultHelpers.FailWithException("Exception parsing value", e);
                        }
                    }
                    else
                    {
                        return Result.Fail("Value not a boolean");
                    }
                }

                return Result.Fail("Value not set");
            }
            else
            {
                return Result.Fail("No item fetched");
            }
        }

        public Result<DateTime?> GetDateTimeValue(string fieldName)
        {
            if (null != listItem?.Fields?.AdditionalData)
            {
                if (listItem.Fields.AdditionalData.ContainsKey(fieldName))
                {
                    string? value = listItem.Fields?.AdditionalData[fieldName]?.ToString();
                    if (value != null)
                    {
                        try
                        {
                            DateTime? dateTime = DateTime.Parse(value);
                            return Result.Ok(dateTime);
                        }
                        catch (Exception e)
                        {
                            return ResultHelpers.FailWithException("Exception parsing value", e);
                        }
                    }
                    else
                    {
                        return Result.Fail("Value not a valid datetime");
                    }
                }

                return Result.Fail("Value not set");
            }
            else
            {
                return Result.Fail("No item fetched");
            }
        }

        public Result<int> GetIntegerValue(string fieldName)
        {
            if (
                listItem != null
                && listItem.Fields != null
                && listItem.Fields.AdditionalData != null
            )
            {
                if (listItem.Fields.AdditionalData.ContainsKey(fieldName))
                {
                    string? value = listItem.Fields?.AdditionalData[fieldName].ToString();
                    if (value != null)
                    {
                        if (Single.TryParse(value, out float ret))
                        {
                            return Result.Ok((int)ret);
                        }
                        else
                        {
                            return Result.Fail("Couldn't parse value");
                        }
                    }
                    else
                    {
                        return Result.Fail("Value not set");
                    }
                }

                return Result.Fail("Value not set");
            }
            else
            {
                return Result.Fail("No item fetched");
            }
        }

        public Result<List<int>> GetMultiLookupValues(string fieldName)
        {
            if (
                listItem != null
                && listItem.Fields != null
                && listItem.Fields.AdditionalData != null
            )
            {
                if (null != listItem.Fields?.AdditionalData[fieldName])
                {
                    // Multilookups are Microsoft.Kiota.Abstractions.Serialization.UntypedArray
                    UntypedArray? fname =
                        listItem.Fields?.AdditionalData[fieldName] as UntypedArray;
                    if (null == fname)
                    {
                        return Result.Fail("Value not set");
                    }
                    List<int> values = GraphUtils.GetLookupIdsFromMultiLookupFieldAsInt(fname);
                    if (values != null)
                    {
                        return values;
                    }
                    else
                    {
                        return Result.Fail("Value not set");
                    }
                }

                return Result.Fail("Value not set");
            }
            else
            {
                return Result.Fail("No item fetched");
            }
        }

        public Result<int> GetSingleLookupValue(string fieldName)
        {
            return GetIntegerValue(fieldName);
        }
    }
}
