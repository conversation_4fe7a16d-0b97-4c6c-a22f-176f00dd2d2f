using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.DataModel.Generated
{
  public class DocumentType : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
    private string _sourcePath =  "";
    private static string table = "Document Types";
    private static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVGroupGuid",
      "QuickPartTitle",
      "ShortCode",
      "TemplateUrl",
      "ATVGuid",
      "ATVDocClass",
      "ATVDefaultValues",
      "ATVFramework",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
    };
    
    private static string[] boolAttributeNames = new string[]
    {
      "ATVDefaultPCF",
      "ATVConfidential",
    };
    
    private static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string ATVGroupGuid
      {
        get { return GetFieldAsString("ATVGroupGuid");}
        set { SetFieldAsString("ATVGroupGuid",value);}
      }
    
    public string QuickPartTitle
      {
        get { return GetFieldAsString("QuickPartTitle");}
        set { SetFieldAsString("QuickPartTitle",value);}
      }
    
    public string ShortCode
      {
        get { return GetFieldAsString("ShortCode");}
        set { SetFieldAsString("ShortCode",value);}
      }
    
    public string TemplateUrl
      {
        get { return GetFieldAsString("TemplateUrl");}
        set { SetFieldAsString("TemplateUrl",value);}
      }
    
    public string ATVGuid
      {
        get { return GetFieldAsString("ATVGuid");}
        set { SetFieldAsString("ATVGuid",value);}
      }
    
    public string ATVDocClass
      {
        get { return GetFieldAsString("ATVDocClass");}
        set { SetFieldAsString("ATVDocClass",value);}
      }
    
    public string ATVDefaultValues
      {
        get { return GetFieldAsString("ATVDefaultValues");}
        set { SetFieldAsString("ATVDefaultValues",value);}
      }
    
    public string ATVFramework
      {
        get { return GetFieldAsString("ATVFramework");}
        set { SetFieldAsString("ATVFramework",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public bool ATVDefaultPCF
      {
        get { return GetFieldAsBool("ATVDefaultPCF");}
        set { SetFieldAsBool("ATVDefaultPCF",value);}
      }
    
    public bool ATVConfidential
      {
        get { return GetFieldAsBool("ATVConfidential");}
        set { SetFieldAsBool("ATVConfidential",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private DocumentType(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public DocumentType(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public DocumentType()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<DocumentType>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(id,table,
                
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new DocumentType(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
        return Result.Fail("There was an error getting a DocumentType by ID");
    }

    public static async Task<Result<List<DocumentType>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<DocumentType> mappedItems = new List<DocumentType>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new DocumentType(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<DocumentType>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new DocumentType(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the DocumentType");
        return Result.Fail("There was an error creating the DocumentType");
      }
    }

    public new async Task<Result<ListItem>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    

    public new async Task<Result> Delete(Context ctx)
    {
      Result limRes = await ctx.GraphApiClient.DeleteListItem(GetId().ToString(), table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    /*
    public static Result Write(List<DocumentType> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVGroupGuid","QuickPartTitle","ShortCode","TemplateUrl","ATVGuid","ATVDocClass","ATVDefaultValues","ATVFramework","Title","ATVDefaultPCF","ATVConfidential");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<DocumentType> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<DocumentType>().ToList();
    }
*/
       }
}
