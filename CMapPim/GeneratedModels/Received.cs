using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.DataModel.Generated
{
  public class Received : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
    private string _sourcePath =  "";
    private static string table = "Received";
    private static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVReceivedCompanyGuid",
      "ATVReceivedContactGuid",
      "ATVTransmittalReference",
      "ATVReceivedReference",
      "ATVReceivedOn",
      "ATVUploadedBy",
      "ATVReceivedMethod",
      "ATVConfidential",
      "ATVReceivedGuid",
      "ATVReceivedCompanyName",
      "ATVReceivedContactName",
      "ATVReceivedNamingScheme",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
    };
    
    private static string[] boolAttributeNames = new string[]
    {
    };
    
    private static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string ATVReceivedCompanyGuid
      {
        get { return GetFieldAsString("ATVReceivedCompanyGuid");}
        set { SetFieldAsString("ATVReceivedCompanyGuid",value);}
      }
    
    public string ATVReceivedContactGuid
      {
        get { return GetFieldAsString("ATVReceivedContactGuid");}
        set { SetFieldAsString("ATVReceivedContactGuid",value);}
      }
    
    public string ATVTransmittalReference
      {
        get { return GetFieldAsString("ATVTransmittalReference");}
        set { SetFieldAsString("ATVTransmittalReference",value);}
      }
    
    public string ATVReceivedReference
      {
        get { return GetFieldAsString("ATVReceivedReference");}
        set { SetFieldAsString("ATVReceivedReference",value);}
      }
    
    public string ATVReceivedOn
      {
        get { return GetFieldAsString("ATVReceivedOn");}
        set { SetFieldAsString("ATVReceivedOn",value);}
      }
    
    public string ATVUploadedBy
      {
        get { return GetFieldAsString("ATVUploadedBy");}
        set { SetFieldAsString("ATVUploadedBy",value);}
      }
    
    public string ATVReceivedMethod
      {
        get { return GetFieldAsString("ATVReceivedMethod");}
        set { SetFieldAsString("ATVReceivedMethod",value);}
      }
    
    public string ATVConfidential
      {
        get { return GetFieldAsString("ATVConfidential");}
        set { SetFieldAsString("ATVConfidential",value);}
      }
    
    public string ATVReceivedGuid
      {
        get { return GetFieldAsString("ATVReceivedGuid");}
        set { SetFieldAsString("ATVReceivedGuid",value);}
      }
    
    public string ATVReceivedCompanyName
      {
        get { return GetFieldAsString("ATVReceivedCompanyName");}
        set { SetFieldAsString("ATVReceivedCompanyName",value);}
      }
    
    public string ATVReceivedContactName
      {
        get { return GetFieldAsString("ATVReceivedContactName");}
        set { SetFieldAsString("ATVReceivedContactName",value);}
      }
    
    public string ATVReceivedNamingScheme
      {
        get { return GetFieldAsString("ATVReceivedNamingScheme");}
        set { SetFieldAsString("ATVReceivedNamingScheme",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private Received(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public Received(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public Received()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<Received>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(id,table,
                
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new Received(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
        return Result.Fail("There was an error getting a Received by ID");
    }

    public static async Task<Result<List<Received>>> Find(
      Context ctx,
      string filter,
      string expand
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<Received> mappedItems = new List<Received>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new Received(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<Received>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new Received(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the Received");
        return Result.Fail("There was an error creating the Received");
      }
    }

    public new async Task<Result<ListItem>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    

    public new async Task<Result> Delete(Context ctx)
    {
      Result limRes = await ctx.GraphApiClient.DeleteListItem(GetId().ToString(), table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    /*
    public static Result Write(List<Received> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVReceivedCompanyGuid","ATVReceivedContactGuid","ATVTransmittalReference","ATVReceivedReference","ATVReceivedOn","ATVUploadedBy","ATVReceivedMethod","ATVConfidential","ATVReceivedGuid","ATVReceivedCompanyName","ATVReceivedContactName","ATVReceivedNamingScheme","Title");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<Received> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<Received>().ToList();
    }
*/
       }
}
