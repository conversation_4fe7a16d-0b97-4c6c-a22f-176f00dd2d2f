using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.DataModel.Generated
{
  public class Project : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
    private string _sourcePath =  "";
    private static string table = "Projects";
    private static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "Description",
      "StatusMessage",
      "ProjectNumber",
      "Category",
      "NumberingScheme",
      "ATVProposedBy",
      "ATVClientName",
      "ATVClientCompany",
      "ATVDevelopmentType",
      "ATVApproxArea",
      "ATVCity",
      "ATVCountry",
      "ATVResponsibleDirector",
      "ATVAssociateDirector",
      "ATVD4ResponsibilityContact",
      "ATVAdminRequestor",
      "ATVSector",
      "ATVServices",
      "ATVWorkFee",
      "ProjectSiteRelativeUrl",
      "ATVCompanyGuid",
      "ATVAddressGuid",
      "ATVLongDescription",
      "ATVRapportProjectId",
      "ATVProjectStatus",
      "ATVBim360Context",
      "ATVProjectComment",
      "ATVQMSStage",
      "ATVQMSSpecification",
      "ATVArchiveStatus",
      "ATVOptionalListProvisioning",
      "ATVFramework",
      "ATVSitePostcode",
      "QAStage",
      "QATaskSet",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
      "ATVSchemaVersion",
      "ATVQMSStageId",
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
      "ATVAddressLookupLookupId",
      "ATVCompanyLookupLookupId",
      "ATVClientCompanyLookupLookupId",
      "ATVProposedByLookupLookupId",
      "ATVClientContactLookupLookupId",
      "ATVResponsibleDirectorLookupLookupId",
      "ATVAssociateDirectorLookupLookupId",
      "ATVD4ResponsibilityContactLookupLookupId",
      "ATVAdminRequestorLookupLookupId",
    };
    
    private static string[] boolAttributeNames = new string[]
    {
      "Provisioned",
      "ATVIsRapportProject",
      "ATVIsProjectEnquiryForm",
      "ATVIsNewClient",
      "ATVDisableNaming",
      "ATVProjectFilesInSharePoint",
      "ATVShouldWorkFlowTasksRun",
    };
    
    private static string[] dateTimeAttributeNames = new string[]
    {
      "ATVStartDate",
    };
  
    public string Description
      {
        get { return GetFieldAsString("Description");}
        set { SetFieldAsString("Description",value);}
      }
    
    public string StatusMessage
      {
        get { return GetFieldAsString("StatusMessage");}
        set { SetFieldAsString("StatusMessage",value);}
      }
    
    public string ProjectNumber
      {
        get { return GetFieldAsString("ProjectNumber");}
        set { SetFieldAsString("ProjectNumber",value);}
      }
    
    public string Category
      {
        get { return GetFieldAsString("Category");}
        set { SetFieldAsString("Category",value);}
      }
    
    public string NumberingScheme
      {
        get { return GetFieldAsString("NumberingScheme");}
        set { SetFieldAsString("NumberingScheme",value);}
      }
    
    public string ATVProposedBy
      {
        get { return GetFieldAsString("ATVProposedBy");}
        set { SetFieldAsString("ATVProposedBy",value);}
      }
    
    public string ATVClientName
      {
        get { return GetFieldAsString("ATVClientName");}
        set { SetFieldAsString("ATVClientName",value);}
      }
    
    public string ATVClientCompany
      {
        get { return GetFieldAsString("ATVClientCompany");}
        set { SetFieldAsString("ATVClientCompany",value);}
      }
    
    public string ATVDevelopmentType
      {
        get { return GetFieldAsString("ATVDevelopmentType");}
        set { SetFieldAsString("ATVDevelopmentType",value);}
      }
    
    public string ATVApproxArea
      {
        get { return GetFieldAsString("ATVApproxArea");}
        set { SetFieldAsString("ATVApproxArea",value);}
      }
    
    public string ATVCity
      {
        get { return GetFieldAsString("ATVCity");}
        set { SetFieldAsString("ATVCity",value);}
      }
    
    public string ATVCountry
      {
        get { return GetFieldAsString("ATVCountry");}
        set { SetFieldAsString("ATVCountry",value);}
      }
    
    public string ATVResponsibleDirector
      {
        get { return GetFieldAsString("ATVResponsibleDirector");}
        set { SetFieldAsString("ATVResponsibleDirector",value);}
      }
    
    public string ATVAssociateDirector
      {
        get { return GetFieldAsString("ATVAssociateDirector");}
        set { SetFieldAsString("ATVAssociateDirector",value);}
      }
    
    public string ATVD4ResponsibilityContact
      {
        get { return GetFieldAsString("ATVD4ResponsibilityContact");}
        set { SetFieldAsString("ATVD4ResponsibilityContact",value);}
      }
    
    public string ATVAdminRequestor
      {
        get { return GetFieldAsString("ATVAdminRequestor");}
        set { SetFieldAsString("ATVAdminRequestor",value);}
      }
    
    public string ATVSector
      {
        get { return GetFieldAsString("ATVSector");}
        set { SetFieldAsString("ATVSector",value);}
      }
    
    public string ATVServices
      {
        get { return GetFieldAsString("ATVServices");}
        set { SetFieldAsString("ATVServices",value);}
      }
    
    public string ATVWorkFee
      {
        get { return GetFieldAsString("ATVWorkFee");}
        set { SetFieldAsString("ATVWorkFee",value);}
      }
    
    public string ProjectSiteRelativeUrl
      {
        get { return GetFieldAsString("ProjectSiteRelativeUrl");}
        set { SetFieldAsString("ProjectSiteRelativeUrl",value);}
      }
    
    public string ATVCompanyGuid
      {
        get { return GetFieldAsString("ATVCompanyGuid");}
        set { SetFieldAsString("ATVCompanyGuid",value);}
      }
    
    public string ATVAddressGuid
      {
        get { return GetFieldAsString("ATVAddressGuid");}
        set { SetFieldAsString("ATVAddressGuid",value);}
      }
    
    public string ATVLongDescription
      {
        get { return GetFieldAsString("ATVLongDescription");}
        set { SetFieldAsString("ATVLongDescription",value);}
      }
    
    public string ATVRapportProjectId
      {
        get { return GetFieldAsString("ATVRapportProjectId");}
        set { SetFieldAsString("ATVRapportProjectId",value);}
      }
    
    public string ATVProjectStatus
      {
        get { return GetFieldAsString("ATVProjectStatus");}
        set { SetFieldAsString("ATVProjectStatus",value);}
      }
    
    public string ATVBim360Context
      {
        get { return GetFieldAsString("ATVBim360Context");}
        set { SetFieldAsString("ATVBim360Context",value);}
      }
    
    public string ATVProjectComment
      {
        get { return GetFieldAsString("ATVProjectComment");}
        set { SetFieldAsString("ATVProjectComment",value);}
      }
    
    public string ATVQMSStage
      {
        get { return GetFieldAsString("ATVQMSStage");}
        set { SetFieldAsString("ATVQMSStage",value);}
      }
    
    public string ATVQMSSpecification
      {
        get { return GetFieldAsString("ATVQMSSpecification");}
        set { SetFieldAsString("ATVQMSSpecification",value);}
      }
    
    public string ATVArchiveStatus
      {
        get { return GetFieldAsString("ATVArchiveStatus");}
        set { SetFieldAsString("ATVArchiveStatus",value);}
      }
    
    public string ATVOptionalListProvisioning
      {
        get { return GetFieldAsString("ATVOptionalListProvisioning");}
        set { SetFieldAsString("ATVOptionalListProvisioning",value);}
      }
    
    public string ATVFramework
      {
        get { return GetFieldAsString("ATVFramework");}
        set { SetFieldAsString("ATVFramework",value);}
      }
    
    public string ATVSitePostcode
      {
        get { return GetFieldAsString("ATVSitePostcode");}
        set { SetFieldAsString("ATVSitePostcode",value);}
      }
    
    public string QAStage
      {
        get { return GetFieldAsString("QAStage");}
        set { SetFieldAsString("QAStage",value);}
      }
    
    public string QATaskSet
      {
        get { return GetFieldAsString("QATaskSet");}
        set { SetFieldAsString("QATaskSet",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public int ATVSchemaVersion
      {
        get { return GetFieldAsInteger("ATVSchemaVersion");}
        set { SetFieldAsInteger("ATVSchemaVersion",value);}
      }
    
    public int ATVQMSStageId
      {
        get { return GetFieldAsInteger("ATVQMSStageId");}
        set { SetFieldAsInteger("ATVQMSStageId",value);}
      }
    
    public int ATVAddressLookup
      {
        get { return GetFieldAsSingleLookup("ATVAddressLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVAddressLookupLookupId",value);}
      }
    
    public int ATVCompanyLookup
      {
        get { return GetFieldAsSingleLookup("ATVCompanyLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVCompanyLookupLookupId",value);}
      }
    
    public int ATVClientCompanyLookup
      {
        get { return GetFieldAsSingleLookup("ATVClientCompanyLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVClientCompanyLookupLookupId",value);}
      }
    
    public int ATVProposedByLookup
      {
        get { return GetFieldAsSingleLookup("ATVProposedByLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVProposedByLookupLookupId",value);}
      }
    
    public int ATVClientContactLookup
      {
        get { return GetFieldAsSingleLookup("ATVClientContactLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVClientContactLookupLookupId",value);}
      }
    
    public int ATVResponsibleDirectorLookup
      {
        get { return GetFieldAsSingleLookup("ATVResponsibleDirectorLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVResponsibleDirectorLookupLookupId",value);}
      }
    
    public int ATVAssociateDirectorLookup
      {
        get { return GetFieldAsSingleLookup("ATVAssociateDirectorLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVAssociateDirectorLookupLookupId",value);}
      }
    
    public int ATVD4ResponsibilityContactLookup
      {
        get { return GetFieldAsSingleLookup("ATVD4ResponsibilityContactLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVD4ResponsibilityContactLookupLookupId",value);}
      }
    
    public int ATVAdminRequestorLookup
      {
        get { return GetFieldAsSingleLookup("ATVAdminRequestorLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVAdminRequestorLookupLookupId",value);}
      }
    
    public bool Provisioned
      {
        get { return GetFieldAsBool("Provisioned");}
        set { SetFieldAsBool("Provisioned",value);}
      }
    
    public bool ATVIsRapportProject
      {
        get { return GetFieldAsBool("ATVIsRapportProject");}
        set { SetFieldAsBool("ATVIsRapportProject",value);}
      }
    
    public bool ATVIsProjectEnquiryForm
      {
        get { return GetFieldAsBool("ATVIsProjectEnquiryForm");}
        set { SetFieldAsBool("ATVIsProjectEnquiryForm",value);}
      }
    
    public bool ATVIsNewClient
      {
        get { return GetFieldAsBool("ATVIsNewClient");}
        set { SetFieldAsBool("ATVIsNewClient",value);}
      }
    
    public bool ATVDisableNaming
      {
        get { return GetFieldAsBool("ATVDisableNaming");}
        set { SetFieldAsBool("ATVDisableNaming",value);}
      }
    
    public bool ATVProjectFilesInSharePoint
      {
        get { return GetFieldAsBool("ATVProjectFilesInSharePoint");}
        set { SetFieldAsBool("ATVProjectFilesInSharePoint",value);}
      }
    
    public bool ATVShouldWorkFlowTasksRun
      {
        get { return GetFieldAsBool("ATVShouldWorkFlowTasksRun");}
        set { SetFieldAsBool("ATVShouldWorkFlowTasksRun",value);}
      }
    
    public DateTime? ATVStartDate
      {
        get { return GetFieldAsDateTime("ATVStartDate");}
        set { SetFieldAsDateTime("ATVStartDate",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private Project(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public Project(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public Project()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<Project>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(id,table,
                
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new Project(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
        return Result.Fail("There was an error getting a Project by ID");
    }

    public static async Task<Result<List<Project>>> Find(
      Context ctx,
      string filter,
      string expand
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<Project> mappedItems = new List<Project>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new Project(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<Project>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new Project(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the Project");
        return Result.Fail("There was an error creating the Project");
      }
    }

    public new async Task<Result<ListItem>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    

    public new async Task<Result> Delete(Context ctx)
    {
      Result limRes = await ctx.GraphApiClient.DeleteListItem(GetId().ToString(), table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    /*
    public static Result Write(List<Project> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("Description","StatusMessage","ProjectNumber","Category","NumberingScheme","ATVProposedBy","ATVClientName","ATVClientCompany","ATVDevelopmentType","ATVApproxArea","ATVCity","ATVCountry","ATVResponsibleDirector","ATVAssociateDirector","ATVD4ResponsibilityContact","ATVAdminRequestor","ATVSector","ATVServices","ATVWorkFee","ProjectSiteRelativeUrl","ATVCompanyGuid","ATVAddressGuid","ATVLongDescription","ATVRapportProjectId","ATVProjectStatus","ATVBim360Context","ATVProjectComment","ATVQMSStage","ATVQMSSpecification","ATVArchiveStatus","ATVOptionalListProvisioning","ATVFramework","ATVSitePostcode","QAStage","QATaskSet","Title","ATVSchemaVersion","ATVQMSStageId","ATVAddressLookup","ATVCompanyLookup","ATVClientCompanyLookup","ATVProposedByLookup","ATVClientContactLookup","ATVResponsibleDirectorLookup","ATVAssociateDirectorLookup","ATVD4ResponsibilityContactLookup","ATVAdminRequestorLookup","Provisioned","ATVIsRapportProject","ATVIsProjectEnquiryForm","ATVIsNewClient","ATVDisableNaming","ATVProjectFilesInSharePoint","ATVShouldWorkFlowTasksRun","ATVStartDate");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<Project> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<Project>().ToList();
    }
*/
       }
}
