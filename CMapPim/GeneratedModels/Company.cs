using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.DataModel.Generated
{
  public class Company : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
    private string _sourcePath =  "";
    private static string table = "Company";
    private static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVCompanyWebsite",
      "ATVParentGuid",
      "ATVCompanyCode",
      "ATVCompanyGuid",
      "ATVHideReason",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
      "ATVParentId",
      "ATVProjectRoleId",
      "ATVRapportId",
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
    };
    
    private static string[] boolAttributeNames = new string[]
    {
      "ATVInternalCompany",
    };
    
    private static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string ATVCompanyWebsite
      {
        get { return GetFieldAsString("ATVCompanyWebsite");}
        set { SetFieldAsString("ATVCompanyWebsite",value);}
      }
    
    public string ATVParentGuid
      {
        get { return GetFieldAsString("ATVParentGuid");}
        set { SetFieldAsString("ATVParentGuid",value);}
      }
    
    public string ATVCompanyCode
      {
        get { return GetFieldAsString("ATVCompanyCode");}
        set { SetFieldAsString("ATVCompanyCode",value);}
      }
    
    public string ATVCompanyGuid
      {
        get { return GetFieldAsString("ATVCompanyGuid");}
        set { SetFieldAsString("ATVCompanyGuid",value);}
      }
    
    public string ATVHideReason
      {
        get { return GetFieldAsString("ATVHideReason");}
        set { SetFieldAsString("ATVHideReason",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public int ATVParentId
      {
        get { return GetFieldAsInteger("ATVParentId");}
        set { SetFieldAsInteger("ATVParentId",value);}
      }
    
    public int ATVProjectRoleId
      {
        get { return GetFieldAsInteger("ATVProjectRoleId");}
        set { SetFieldAsInteger("ATVProjectRoleId",value);}
      }
    
    public int ATVRapportId
      {
        get { return GetFieldAsInteger("ATVRapportId");}
        set { SetFieldAsInteger("ATVRapportId",value);}
      }
    
    public bool ATVInternalCompany
      {
        get { return GetFieldAsBool("ATVInternalCompany");}
        set { SetFieldAsBool("ATVInternalCompany",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private Company(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public Company(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public Company()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<Company>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,
                
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new Company(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else{
        return limRes.ToResult();
      }
    }

    public static async Task<Result<List<Company>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<Company> mappedItems = new List<Company>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new Company(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<Company>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new Company(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the Company");
        return Result.Fail("There was an error creating the Company");
      }
    }

    public new async Task<Result<ListItem>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    

    public new async Task<Result> Delete(Context ctx)
    {
      Result limRes = await ctx.GraphApiClient.DeleteListItem(GetId().ToString(), table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    /*
    public static Result Write(List<Company> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVCompanyWebsite","ATVParentGuid","ATVCompanyCode","ATVCompanyGuid","ATVHideReason","Title","ATVParentId","ATVProjectRoleId","ATVRapportId","ATVInternalCompany");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<Company> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<Company>().ToList();
    }
*/
       }
}
