using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.DataModel.Generated
{
  public class RequiredField : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
    private string _sourcePath =  "";
    private static string table = "RequiredFields";
    private static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVFieldId",
      "ATVFormId",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
    };
    
    private static string[] boolAttributeNames = new string[]
    {
      "ATVRequired",
      "ATVHidden",
    };
    
    private static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string ATVFieldId
      {
        get { return GetFieldAsString("ATVFieldId");}
        set { SetFieldAsString("ATVFieldId",value);}
      }
    
    public string ATVFormId
      {
        get { return GetFieldAsString("ATVFormId");}
        set { SetFieldAsString("ATVFormId",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public bool ATVRequired
      {
        get { return GetFieldAsBool("ATVRequired");}
        set { SetFieldAsBool("ATVRequired",value);}
      }
    
    public bool ATVHidden
      {
        get { return GetFieldAsBool("ATVHidden");}
        set { SetFieldAsBool("ATVHidden",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private RequiredField(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public RequiredField(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public RequiredField()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<RequiredField>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(id,table,
                
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new RequiredField(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
        return Result.Fail("There was an error getting a RequiredField by ID");
    }

    public static async Task<Result<List<RequiredField>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<RequiredField> mappedItems = new List<RequiredField>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new RequiredField(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<RequiredField>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new RequiredField(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the RequiredField");
        return Result.Fail("There was an error creating the RequiredField");
      }
    }

    public new async Task<Result<ListItem>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    

    public new async Task<Result> Delete(Context ctx)
    {
      Result limRes = await ctx.GraphApiClient.DeleteListItem(GetId().ToString(), table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    /*
    public static Result Write(List<RequiredField> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVFieldId","ATVFormId","Title","ATVRequired","ATVHidden");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<RequiredField> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<RequiredField>().ToList();
    }
*/
       }
}
