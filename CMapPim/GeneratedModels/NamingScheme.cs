using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.DataModel.Generated
{
  public class NamingScheme : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
    private string _sourcePath =  "";
    private static string table = "NamingSchemes";
    private static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "Description",
      "ATVGuid",
      "ATVDLMNameBlockTemplate",
      "ATVDLMNumberBlockTemplate",
      "ATVDLMOptionBlockTemplate",
      "ATVDMSNameBlockTemplate",
      "ATVDMSNumberBlockTemplate",
      "ATVDMSOptionBlockTemplate",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
      "ATVSortOrder",
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
    };
    
    private static string[] boolAttributeNames = new string[]
    {
      "ATVDefault",
      "ATVInternal",
    };
    
    private static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string Description
      {
        get { return GetFieldAsString("Description");}
        set { SetFieldAsString("Description",value);}
      }
    
    public string ATVGuid
      {
        get { return GetFieldAsString("ATVGuid");}
        set { SetFieldAsString("ATVGuid",value);}
      }
    
    public string ATVDLMNameBlockTemplate
      {
        get { return GetFieldAsString("ATVDLMNameBlockTemplate");}
        set { SetFieldAsString("ATVDLMNameBlockTemplate",value);}
      }
    
    public string ATVDLMNumberBlockTemplate
      {
        get { return GetFieldAsString("ATVDLMNumberBlockTemplate");}
        set { SetFieldAsString("ATVDLMNumberBlockTemplate",value);}
      }
    
    public string ATVDLMOptionBlockTemplate
      {
        get { return GetFieldAsString("ATVDLMOptionBlockTemplate");}
        set { SetFieldAsString("ATVDLMOptionBlockTemplate",value);}
      }
    
    public string ATVDMSNameBlockTemplate
      {
        get { return GetFieldAsString("ATVDMSNameBlockTemplate");}
        set { SetFieldAsString("ATVDMSNameBlockTemplate",value);}
      }
    
    public string ATVDMSNumberBlockTemplate
      {
        get { return GetFieldAsString("ATVDMSNumberBlockTemplate");}
        set { SetFieldAsString("ATVDMSNumberBlockTemplate",value);}
      }
    
    public string ATVDMSOptionBlockTemplate
      {
        get { return GetFieldAsString("ATVDMSOptionBlockTemplate");}
        set { SetFieldAsString("ATVDMSOptionBlockTemplate",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public int ATVSortOrder
      {
        get { return GetFieldAsInteger("ATVSortOrder");}
        set { SetFieldAsInteger("ATVSortOrder",value);}
      }
    
    public bool ATVDefault
      {
        get { return GetFieldAsBool("ATVDefault");}
        set { SetFieldAsBool("ATVDefault",value);}
      }
    
    public bool ATVInternal
      {
        get { return GetFieldAsBool("ATVInternal");}
        set { SetFieldAsBool("ATVInternal",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private NamingScheme(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public NamingScheme(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public NamingScheme()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<NamingScheme>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,
                
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new NamingScheme(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else{
        return limRes.ToResult();
      }
    }

    public static async Task<Result<List<NamingScheme>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<NamingScheme> mappedItems = new List<NamingScheme>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new NamingScheme(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<NamingScheme>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new NamingScheme(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the NamingScheme");
        return Result.Fail("There was an error creating the NamingScheme");
      }
    }

    public new async Task<Result<ListItem>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    

    public new async Task<Result> Delete(Context ctx)
    {
      Result limRes = await ctx.GraphApiClient.DeleteListItem(GetId().ToString(), table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    /*
    public static Result Write(List<NamingScheme> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("Description","ATVGuid","ATVDLMNameBlockTemplate","ATVDLMNumberBlockTemplate","ATVDLMOptionBlockTemplate","ATVDMSNameBlockTemplate","ATVDMSNumberBlockTemplate","ATVDMSOptionBlockTemplate","Title","ATVSortOrder","ATVDefault","ATVInternal");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<NamingScheme> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<NamingScheme>().ToList();
    }
*/
       }
}
