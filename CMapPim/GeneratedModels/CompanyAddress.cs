using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.DataModel.Generated
{
  public class CompanyAddress : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
    private string _sourcePath =  "";
    private static string table = "CompanyAddress";
    private static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVAddress",
      "ATVAddressLine1",
      "ATVAddressLine2",
      "ATVAddressLine3",
      "ATVCity",
      "ATVCompanyMainPhone",
      "ATVCountry",
      "ATVCounty",
      "ATVEmail",
      "ATVParentGuid",
      "ATVPostCode",
      "ATVCompanyGuid",
      "ATVCompanyName",
      "ATVCompanyAddressCode",
      "ATVCompanyAddressGuid",
      "ATVHideReason",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
      "ATVParentId",
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
      "ATVCompanyLookupLookupId",
    };
    
    private static string[] boolAttributeNames = new string[]
    {
    };
    
    private static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string ATVAddress
      {
        get { return GetFieldAsString("ATVAddress");}
        set { SetFieldAsString("ATVAddress",value);}
      }
    
    public string ATVAddressLine1
      {
        get { return GetFieldAsString("ATVAddressLine1");}
        set { SetFieldAsString("ATVAddressLine1",value);}
      }
    
    public string ATVAddressLine2
      {
        get { return GetFieldAsString("ATVAddressLine2");}
        set { SetFieldAsString("ATVAddressLine2",value);}
      }
    
    public string ATVAddressLine3
      {
        get { return GetFieldAsString("ATVAddressLine3");}
        set { SetFieldAsString("ATVAddressLine3",value);}
      }
    
    public string ATVCity
      {
        get { return GetFieldAsString("ATVCity");}
        set { SetFieldAsString("ATVCity",value);}
      }
    
    public string ATVCompanyMainPhone
      {
        get { return GetFieldAsString("ATVCompanyMainPhone");}
        set { SetFieldAsString("ATVCompanyMainPhone",value);}
      }
    
    public string ATVCountry
      {
        get { return GetFieldAsString("ATVCountry");}
        set { SetFieldAsString("ATVCountry",value);}
      }
    
    public string ATVCounty
      {
        get { return GetFieldAsString("ATVCounty");}
        set { SetFieldAsString("ATVCounty",value);}
      }
    
    public string ATVEmail
      {
        get { return GetFieldAsString("ATVEmail");}
        set { SetFieldAsString("ATVEmail",value);}
      }
    
    public string ATVParentGuid
      {
        get { return GetFieldAsString("ATVParentGuid");}
        set { SetFieldAsString("ATVParentGuid",value);}
      }
    
    public string ATVPostCode
      {
        get { return GetFieldAsString("ATVPostCode");}
        set { SetFieldAsString("ATVPostCode",value);}
      }
    
    public string ATVCompanyGuid
      {
        get { return GetFieldAsString("ATVCompanyGuid");}
        set { SetFieldAsString("ATVCompanyGuid",value);}
      }
    
    public string ATVCompanyName
      {
        get { return GetFieldAsString("ATVCompanyName");}
        set { SetFieldAsString("ATVCompanyName",value);}
      }
    
    public string ATVCompanyAddressCode
      {
        get { return GetFieldAsString("ATVCompanyAddressCode");}
        set { SetFieldAsString("ATVCompanyAddressCode",value);}
      }
    
    public string ATVCompanyAddressGuid
      {
        get { return GetFieldAsString("ATVCompanyAddressGuid");}
        set { SetFieldAsString("ATVCompanyAddressGuid",value);}
      }
    
    public string ATVHideReason
      {
        get { return GetFieldAsString("ATVHideReason");}
        set { SetFieldAsString("ATVHideReason",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public int ATVParentId
      {
        get { return GetFieldAsInteger("ATVParentId");}
        set { SetFieldAsInteger("ATVParentId",value);}
      }
    
    public int ATVCompanyLookup
      {
        get { return GetFieldAsSingleLookup("ATVCompanyLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVCompanyLookupLookupId",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private CompanyAddress(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public CompanyAddress(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public CompanyAddress()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<CompanyAddress>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(id,table,
                
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new CompanyAddress(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
        return Result.Fail("There was an error getting a CompanyAddress by ID");
    }

    public static async Task<Result<List<CompanyAddress>>> Find(
      Context ctx,
      string filter,
      string expand
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<CompanyAddress> mappedItems = new List<CompanyAddress>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new CompanyAddress(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<CompanyAddress>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new CompanyAddress(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the CompanyAddress");
        return Result.Fail("There was an error creating the CompanyAddress");
      }
    }

    public new async Task<Result<ListItem>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    

    public new async Task<Result> Delete(Context ctx)
    {
      Result limRes = await ctx.GraphApiClient.DeleteListItem(GetId().ToString(), table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    /*
    public static Result Write(List<CompanyAddress> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVAddress","ATVAddressLine1","ATVAddressLine2","ATVAddressLine3","ATVCity","ATVCompanyMainPhone","ATVCountry","ATVCounty","ATVEmail","ATVParentGuid","ATVPostCode","ATVCompanyGuid","ATVCompanyName","ATVCompanyAddressCode","ATVCompanyAddressGuid","ATVHideReason","Title","ATVParentId","ATVCompanyLookup");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<CompanyAddress> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<CompanyAddress>().ToList();
    }
*/
       }
}
