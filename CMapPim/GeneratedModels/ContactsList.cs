using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.DataModel.Generated
{
  public class ContactsList : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
    private string _sourcePath =  "";
    private static string table = "ContactsLists";
    private static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "Description",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
      "ATVContacts",
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
      "ATVProjectLookupId",
    };
    
    private static string[] boolAttributeNames = new string[]
    {
    };
    
    private static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string Description
      {
        get { return GetFieldAsString("Description");}
        set { SetFieldAsString("Description",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    


      public string ATVContactsAsString
      {
        get {
          List<int> ids = GetFieldAsMultiLookup("ATVContacts");
          string str = "";
          foreach (int id in ids) {
            str = str + id.ToString() + ",";
          }
          return str;
        }

        set {

          if (value != null && value != "") {

            List<int> ids = new List<int>();
            string[] idstr = value.Split(",");
            foreach (string id in idstr) {
              if (id != "")
              ids.Add(Int32.Parse(id));
            }
            SetFieldAsMultiLookup("ATVContacts",ids);
          }
        }

      }

          public int ATVProject
      {
        get { return GetFieldAsSingleLookup("ATVProjectLookupId");}
        set { SetFieldAsSingleLookup("ATVProjectLookupId",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private ContactsList(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public ContactsList(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public ContactsList()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<ContactsList>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(id,table,
                
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new ContactsList(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
        return Result.Fail("There was an error getting a ContactsList by ID");
    }

    public static async Task<Result<List<ContactsList>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<ContactsList> mappedItems = new List<ContactsList>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new ContactsList(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<ContactsList>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new ContactsList(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the ContactsList");
        return Result.Fail("There was an error creating the ContactsList");
      }
    }

    public new async Task<Result<ListItem>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    

    public new async Task<Result> Delete(Context ctx)
    {
      Result limRes = await ctx.GraphApiClient.DeleteListItem(GetId().ToString(), table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    /*
    public static Result Write(List<ContactsList> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("Description","Title","ATVContacts","ATVProject");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<ContactsList> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<ContactsList>().ToList();
    }
*/
       }
}
