using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.DataModel.Generated
{
  public class NameBlock : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
    private string _sourcePath =  "";
    private static string table = "NameBlocks";
    private static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVDelimiter",
      "ATVFieldName",
      "ATVScheme",
      "ATVGuid",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
      "ATVLength",
      "ATVPosition",
      "ATVSortOrder",
      "ATVTemplateType",
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
    };
    
    private static string[] boolAttributeNames = new string[]
    {
      "ATVDmsRequired",
      "ATVHyphen",
      "ATVPrefix",
      "ATVRequired",
    };
    
    private static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string ATVDelimiter
      {
        get { return GetFieldAsString("ATVDelimiter");}
        set { SetFieldAsString("ATVDelimiter",value);}
      }
    
    public string ATVFieldName
      {
        get { return GetFieldAsString("ATVFieldName");}
        set { SetFieldAsString("ATVFieldName",value);}
      }
    
    public string ATVScheme
      {
        get { return GetFieldAsString("ATVScheme");}
        set { SetFieldAsString("ATVScheme",value);}
      }
    
    public string ATVGuid
      {
        get { return GetFieldAsString("ATVGuid");}
        set { SetFieldAsString("ATVGuid",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public int ATVLength
      {
        get { return GetFieldAsInteger("ATVLength");}
        set { SetFieldAsInteger("ATVLength",value);}
      }
    
    public int ATVPosition
      {
        get { return GetFieldAsInteger("ATVPosition");}
        set { SetFieldAsInteger("ATVPosition",value);}
      }
    
    public int ATVSortOrder
      {
        get { return GetFieldAsInteger("ATVSortOrder");}
        set { SetFieldAsInteger("ATVSortOrder",value);}
      }
    
    public int ATVTemplateType
      {
        get { return GetFieldAsInteger("ATVTemplateType");}
        set { SetFieldAsInteger("ATVTemplateType",value);}
      }
    
    public bool ATVDmsRequired
      {
        get { return GetFieldAsBool("ATVDmsRequired");}
        set { SetFieldAsBool("ATVDmsRequired",value);}
      }
    
    public bool ATVHyphen
      {
        get { return GetFieldAsBool("ATVHyphen");}
        set { SetFieldAsBool("ATVHyphen",value);}
      }
    
    public bool ATVPrefix
      {
        get { return GetFieldAsBool("ATVPrefix");}
        set { SetFieldAsBool("ATVPrefix",value);}
      }
    
    public bool ATVRequired
      {
        get { return GetFieldAsBool("ATVRequired");}
        set { SetFieldAsBool("ATVRequired",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private NameBlock(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public NameBlock(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public NameBlock()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<NameBlock>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(id,table,
                
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new NameBlock(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
        return Result.Fail("There was an error getting a NameBlock by ID");
    }

    public static async Task<Result<List<NameBlock>>> Find(
      Context ctx,
      string filter,
      string expand
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<NameBlock> mappedItems = new List<NameBlock>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new NameBlock(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<NameBlock>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new NameBlock(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the NameBlock");
        return Result.Fail("There was an error creating the NameBlock");
      }
    }

    public new async Task<Result<ListItem>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    

    public new async Task<Result> Delete(Context ctx)
    {
      Result limRes = await ctx.GraphApiClient.DeleteListItem(GetId().ToString(), table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    /*
    public static Result Write(List<NameBlock> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVDelimiter","ATVFieldName","ATVScheme","ATVGuid","Title","ATVLength","ATVPosition","ATVSortOrder","ATVTemplateType","ATVDmsRequired","ATVHyphen","ATVPrefix","ATVRequired");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<NameBlock> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<NameBlock>().ToList();
    }
*/
       }
}
