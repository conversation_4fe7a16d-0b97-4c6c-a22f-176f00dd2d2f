using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.DataModel.Generated
{
  public class ConfidentialDMSFile : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
    private string _sourcePath =  "";
    private static string table = "Confidential_DMS_Library";
    private static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVSearchRefiner",
      "ATVServerRelativeUrl",
      "ATVFileLength",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
    };
    
    private static string[] boolAttributeNames = new string[]
    {
    };
    
    private static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string ATVSearchRefiner
      {
        get { return GetFieldAsString("ATVSearchRefiner");}
        set { SetFieldAsString("ATVSearchRefiner",value);}
      }
    
    public string ATVServerRelativeUrl
      {
        get { return GetFieldAsString("ATVServerRelativeUrl");}
        set { SetFieldAsString("ATVServerRelativeUrl",value);}
      }
    
    public string ATVFileLength
      {
        get { return GetFieldAsString("ATVFileLength");}
        set { SetFieldAsString("ATVFileLength",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    

      private string _libraryId;
      private string GetLibraryID(Context ctx) {
        if (_libraryId == null) {
          CMapPim.Model.DocumentLibrary documentLibrary = new CMapPim.Model.DocumentLibrary(
                    "Confidential_DMS_Library"
                  );

          Result<string> id = documentLibrary.GetId(ctx).GetAwaiter().GetResult();

          if (id.IsFailed)
          {
            return null;
          } else {
            _libraryId = id.Value;
            return _libraryId;
          }
        } else return _libraryId;
      }

      public string SourcePath {
        get ; set;
      }
/*
      public string GetFilePath(IGraphApiClient graphContext)
      {


          if (_sourcePath == "") {

             CMapPim.Model.DocumentLibrary driveLibrary = new CMapPim.Model.DocumentLibrary(
              graphContext,
              "Confidential_DMS_Library"
            );

            Result<Microsoft.Graph.DriveItem> driveItem =
             CMapPim.Model.DriveItemMeta.GetDriveItemByListId(
              graphContext,
              driveLibrary,
              GetId().ToString()
            ).GetAwaiter()
            .GetResult();

            if (driveItem.IsSuccess)
            {
              string parent = driveItem.Value.ParentReference.Path;
              int pos = parent.IndexOf(":");
              string cleanedParent = parent.Substring(pos + 1);
              _sourcePath = cleanedParent + "/" + driveItem.Value.Name;
            }

            return _sourcePath;

          }  else {
            return _sourcePath;
          }


      }



      public static Result<string> GetIDByPath(string path, IGraphApiClient graphContext)
      {
        Console.WriteLine("GetIDByPath for " + path);

        string libraryID = GetLibraryID();
        if (libraryID != null) {
          try
          {

            Microsoft.Graph.DriveItem item = graphContext.GetGraphClient().Drives[libraryID].Root
              .ItemWithPath(path)
              .Request()
              .Expand("ListItem")
              .GetAsync()
              .GetAwaiter()
              .GetResult();

            if (item != null)
            {
              return Result.Ok(item.ListItem.Id.ToString());
            }
            else
            {
              Console.WriteLine("Failed to find file by path");
              return Result.Fail("Unable to find item in drive");
            }
          }
          catch (Exception ex)
          {
            Console.WriteLine("Failed to find file by path");
            return Result.Fail("Unable to find item in drive");
          }
        } else {
           Console.WriteLine("Failed to find  document library");
          return Result.Fail("Failed to find  document library");
        }


      }

*/
    
      private ConfidentialDMSFile(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public ConfidentialDMSFile(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public ConfidentialDMSFile()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<ConfidentialDMSFile>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,
                
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new ConfidentialDMSFile(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else{
        return limRes.ToResult();
      }
    }

    public static async Task<Result<List<ConfidentialDMSFile>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<ConfidentialDMSFile> mappedItems = new List<ConfidentialDMSFile>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new ConfidentialDMSFile(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<ConfidentialDMSFile>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new ConfidentialDMSFile(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the ConfidentialDMSFile");
        return Result.Fail("There was an error creating the ConfidentialDMSFile");
      }
    }

    public new async Task<Result<ListItem>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    

    public new async Task<Result> Delete(Context ctx)
    {
      Result limRes = await ctx.GraphApiClient.DeleteListItem(GetId().ToString(), table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    /*
    public static Result Write(List<ConfidentialDMSFile> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVSearchRefiner","ATVServerRelativeUrl","ATVFileLength","Title");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<ConfidentialDMSFile> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<ConfidentialDMSFile>().ToList();
    }
*/
       }
}
