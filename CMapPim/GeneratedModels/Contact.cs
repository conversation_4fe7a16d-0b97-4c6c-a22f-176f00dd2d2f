using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.DataModel.Generated
{
  public class Contact : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
    private string _sourcePath =  "";
    private static string table = "Contact";
    private static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVParentGuid",
      "ATVSearchRefiner",
      "FullName",
      "FirstNamePhonetic",
      "ATVAddressGuid",
      "ATVCompanyGuid",
      "ATVIsOwnedBy",
      "ATVContactGuid",
      "ATVContactCategory",
      "ATVContactClientType",
      "ATVSupercededString",
      "Title",
      "FirstName",
      "Email",
      "JobTitle",
      "WorkPhone",
      "HomePhone",
      "CellPhone",
    };
    
    private static string[] intAttributes = new string[]
    {
      "ATVProjectRoleId",
      "ATVVersion",
      "ATVRapportId",
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
      "ATVCompanyAddressLookupId",
      "ATVCompanyLookupLookupId",
      "ATVProjectRoleLookupLookupId",
      "ATVProjectRoleCategoryLookupLookupId",
      "ATVOwnerLookupLookupId",
    };
    
    private static string[] boolAttributeNames = new string[]
    {
      "ATVAcceptedGDPR",
      "ATVIsHiddenFlag",
    };
    
    private static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string ATVParentGuid
      {
        get { return GetFieldAsString("ATVParentGuid");}
        set { SetFieldAsString("ATVParentGuid",value);}
      }
    
    public string ATVSearchRefiner
      {
        get { return GetFieldAsString("ATVSearchRefiner");}
        set { SetFieldAsString("ATVSearchRefiner",value);}
      }
    
    public string FullName
      {
        get { return GetFieldAsString("FullName");}
        set { SetFieldAsString("FullName",value);}
      }
    
    public string FirstNamePhonetic
      {
        get { return GetFieldAsString("FirstNamePhonetic");}
        set { SetFieldAsString("FirstNamePhonetic",value);}
      }
    
    public string ATVAddressGuid
      {
        get { return GetFieldAsString("ATVAddressGuid");}
        set { SetFieldAsString("ATVAddressGuid",value);}
      }
    
    public string ATVCompanyGuid
      {
        get { return GetFieldAsString("ATVCompanyGuid");}
        set { SetFieldAsString("ATVCompanyGuid",value);}
      }
    
    public string ATVIsOwnedBy
      {
        get { return GetFieldAsString("ATVIsOwnedBy");}
        set { SetFieldAsString("ATVIsOwnedBy",value);}
      }
    
    public string ATVContactGuid
      {
        get { return GetFieldAsString("ATVContactGuid");}
        set { SetFieldAsString("ATVContactGuid",value);}
      }
    
    public string ATVContactCategory
      {
        get { return GetFieldAsString("ATVContactCategory");}
        set { SetFieldAsString("ATVContactCategory",value);}
      }
    
    public string ATVContactClientType
      {
        get { return GetFieldAsString("ATVContactClientType");}
        set { SetFieldAsString("ATVContactClientType",value);}
      }
    
    public string ATVSupercededString
      {
        get { return GetFieldAsString("ATVSupercededString");}
        set { SetFieldAsString("ATVSupercededString",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public string FirstName
      {
        get { return GetFieldAsString("FirstName");}
        set { SetFieldAsString("FirstName",value);}
      }
    
    public string Email
      {
        get { return GetFieldAsString("Email");}
        set { SetFieldAsString("Email",value);}
      }
    
    public string JobTitle
      {
        get { return GetFieldAsString("JobTitle");}
        set { SetFieldAsString("JobTitle",value);}
      }
    
    public string WorkPhone
      {
        get { return GetFieldAsString("WorkPhone");}
        set { SetFieldAsString("WorkPhone",value);}
      }
    
    public string HomePhone
      {
        get { return GetFieldAsString("HomePhone");}
        set { SetFieldAsString("HomePhone",value);}
      }
    
    public string CellPhone
      {
        get { return GetFieldAsString("CellPhone");}
        set { SetFieldAsString("CellPhone",value);}
      }
    
    public int ATVProjectRoleId
      {
        get { return GetFieldAsInteger("ATVProjectRoleId");}
        set { SetFieldAsInteger("ATVProjectRoleId",value);}
      }
    
    public int ATVVersion
      {
        get { return GetFieldAsInteger("ATVVersion");}
        set { SetFieldAsInteger("ATVVersion",value);}
      }
    
    public int ATVRapportId
      {
        get { return GetFieldAsInteger("ATVRapportId");}
        set { SetFieldAsInteger("ATVRapportId",value);}
      }
    
    public int ATVCompanyAddress
      {
        get { return GetFieldAsSingleLookup("ATVCompanyAddressLookupId");}
        set { SetFieldAsSingleLookup("ATVCompanyAddressLookupId",value);}
      }
    
    public int ATVCompanyLookup
      {
        get { return GetFieldAsSingleLookup("ATVCompanyLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVCompanyLookupLookupId",value);}
      }
    
    public int ATVProjectRoleLookup
      {
        get { return GetFieldAsSingleLookup("ATVProjectRoleLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVProjectRoleLookupLookupId",value);}
      }
    
    public int ATVProjectRoleCategoryLookup
      {
        get { return GetFieldAsSingleLookup("ATVProjectRoleCategoryLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVProjectRoleCategoryLookupLookupId",value);}
      }
    
    public int ATVOwnerLookup
      {
        get { return GetFieldAsSingleLookup("ATVOwnerLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVOwnerLookupLookupId",value);}
      }
    
    public bool ATVAcceptedGDPR
      {
        get { return GetFieldAsBool("ATVAcceptedGDPR");}
        set { SetFieldAsBool("ATVAcceptedGDPR",value);}
      }
    
    public bool ATVIsHiddenFlag
      {
        get { return GetFieldAsBool("ATVIsHiddenFlag");}
        set { SetFieldAsBool("ATVIsHiddenFlag",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private Contact(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public Contact(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public Contact()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<Contact>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(id,table,
                
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new Contact(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
        return Result.Fail("There was an error getting a Contact by ID");
    }

    public static async Task<Result<List<Contact>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteUrl,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<Contact> mappedItems = new List<Contact>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new Contact(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<Contact>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new Contact(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the Contact");
        return Result.Fail("There was an error creating the Contact");
      }
    }

    public new async Task<Result<ListItem>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    

    public new async Task<Result> Delete(Context ctx)
    {
      Result limRes = await ctx.GraphApiClient.DeleteListItem(GetId().ToString(), table, ctx.SiteUrl, ctx.GraphClient);
      return limRes;
    }

    /*
    public static Result Write(List<Contact> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVParentGuid","ATVSearchRefiner","FullName","FirstNamePhonetic","ATVAddressGuid","ATVCompanyGuid","ATVIsOwnedBy","ATVContactGuid","ATVContactCategory","ATVContactClientType","ATVSupercededString","Title","FirstName","Email","JobTitle","WorkPhone","HomePhone","CellPhone","ATVProjectRoleId","ATVVersion","ATVRapportId","ATVCompanyAddress","ATVCompanyLookup","ATVProjectRoleLookup","ATVProjectRoleCategoryLookup","ATVOwnerLookup","ATVAcceptedGDPR","ATVIsHiddenFlag");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<Contact> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<Contact>().ToList();
    }
*/
       }
}
