using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.DataModel.Generated
{
  public class Integration : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
    private string _sourcePath =  "";
    private static string table = "Integrations";
    private static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "BaseUrl",
      "ForeignPasswd",
      "ForeignProject",
      "ForeignUser",
      "IntegrationType",
      "IssueMethod",
      "ATVGuid",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
    };
    
    private static string[] boolAttributeNames = new string[]
    {
      "Enabled",
    };
    
    private static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string BaseUrl
      {
        get { return GetFieldAsString("BaseUrl");}
        set { SetFieldAsString("BaseUrl",value);}
      }
    
    public string ForeignPasswd
      {
        get { return GetFieldAsString("ForeignPasswd");}
        set { SetFieldAsString("ForeignPasswd",value);}
      }
    
    public string ForeignProject
      {
        get { return GetFieldAsString("ForeignProject");}
        set { SetFieldAsString("ForeignProject",value);}
      }
    
    public string ForeignUser
      {
        get { return GetFieldAsString("ForeignUser");}
        set { SetFieldAsString("ForeignUser",value);}
      }
    
    public string IntegrationType
      {
        get { return GetFieldAsString("IntegrationType");}
        set { SetFieldAsString("IntegrationType",value);}
      }
    
    public string IssueMethod
      {
        get { return GetFieldAsString("IssueMethod");}
        set { SetFieldAsString("IssueMethod",value);}
      }
    
    public string ATVGuid
      {
        get { return GetFieldAsString("ATVGuid");}
        set { SetFieldAsString("ATVGuid",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public bool Enabled
      {
        get { return GetFieldAsBool("Enabled");}
        set { SetFieldAsBool("Enabled",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private Integration(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public Integration(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public Integration()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<Integration>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,
                
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new Integration(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else{
        return limRes.ToResult();
      }
    }

    public static async Task<Result<List<Integration>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<Integration> mappedItems = new List<Integration>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new Integration(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<Integration>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new Integration(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the Integration");
        return Result.Fail("There was an error creating the Integration");
      }
    }

    public new async Task<Result<ListItem>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    

    public new async Task<Result> Delete(Context ctx)
    {
      Result limRes = await ctx.GraphApiClient.DeleteListItem(GetId().ToString(), table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    /*
    public static Result Write(List<Integration> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("BaseUrl","ForeignPasswd","ForeignProject","ForeignUser","IntegrationType","IssueMethod","ATVGuid","Title","Enabled");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<Integration> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<Integration>().ToList();
    }
*/
       }
}
