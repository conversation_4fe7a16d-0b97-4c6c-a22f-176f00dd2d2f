using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.DataModel.Generated
{
  public class ProjectFactsheet : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
    private string _sourcePath =  "";
    private static string table = "ProjectFactsheet";
    private static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVMultilineValue",
      "ATVValue",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
      "ATVPosition",
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
    };
    
    private static string[] boolAttributeNames = new string[]
    {
    };
    
    private static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string ATVMultilineValue
      {
        get { return GetFieldAsString("ATVMultilineValue");}
        set { SetFieldAsString("ATVMultilineValue",value);}
      }
    
    public string ATVValue
      {
        get { return GetFieldAsString("ATVValue");}
        set { SetFieldAsString("ATVValue",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public int ATVPosition
      {
        get { return GetFieldAsInteger("ATVPosition");}
        set { SetFieldAsInteger("ATVPosition",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private ProjectFactsheet(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public ProjectFactsheet(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public ProjectFactsheet()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<ProjectFactsheet>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,
                
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new ProjectFactsheet(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else{
        return limRes.ToResult();
      }
    }

    public static async Task<Result<List<ProjectFactsheet>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<ProjectFactsheet> mappedItems = new List<ProjectFactsheet>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new ProjectFactsheet(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<ProjectFactsheet>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new ProjectFactsheet(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the ProjectFactsheet");
        return Result.Fail("There was an error creating the ProjectFactsheet");
      }
    }

    public new async Task<Result<ListItem>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    

    public new async Task<Result> Delete(Context ctx)
    {
      Result limRes = await ctx.GraphApiClient.DeleteListItem(GetId().ToString(), table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    /*
    public static Result Write(List<ProjectFactsheet> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVMultilineValue","ATVValue","Title","ATVPosition");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<ProjectFactsheet> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<ProjectFactsheet>().ToList();
    }
*/
       }
}
