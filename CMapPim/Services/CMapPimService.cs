using CMapPim.DataModel.Generated;
using CMapPim.Model;
using FluentResults;
using Microsoft.Graph;

namespace CMapPim.Services;

public class CMapPimService : ICMapPimService
{
    public async Task<Result<List<RecordSummary>>> GetRecordInformation(
        Context context,
        string startsWith
    )
    {
        Result<List<Placeholder>> placeholders;
        if (string.IsNullOrEmpty(startsWith))
        {
            placeholders = await Placeholder.Find(context, "");
        }
        else
        {
            placeholders = await Placeholder.Find(
                context,
                $"startswith(Fields/ATVDocumentNumber,'{startsWith}')"
            );
        }

        if (placeholders.IsFailed)
        {
            return placeholders.ToResult();
        }

        // Find the last revision name for each record
        List<RecordSummary> recordSummaries = new List<RecordSummary>();
        foreach (Placeholder placeholder in placeholders.Value)
        {
            Result<Revision> revisions = await Revision.GetById(
                context,
                placeholder.ATVLastRevision.ToString()
            );
            if (revisions.IsFailed)
            {
                return revisions.ToResult();
            }
            recordSummaries.Add(
                new()
                {
                    Name = placeholder.Title,
                    Title = placeholder.ATVDocumentTitle,
                    LastRevision = revisions.Value.ATVRevision,
                }
            );
        }
        return Result.Ok(recordSummaries);
    }
}
