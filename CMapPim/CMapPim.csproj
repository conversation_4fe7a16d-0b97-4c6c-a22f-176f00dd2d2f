<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>CMapPim</RootNamespace>

    <!-- Suppress warnings for CMapPim project -->
    <NoWarn>$(NoWarn);NU1701;CS8600;CS8601;CS8602;CS8603;CS8604;CS8618;CS8620;CS0108;CS0109;CS0414</NoWarn>

  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
    <PackageReference Include="FluentResults" Version="3.16.0" />

    <PackageReference Include="Flurl" Version="3.0.7" />
    <PackageReference Include="morelinq" Version="3.4.2" />
    <PackageReference Include="Sylvan.Data" Version="0.2.12" />
    <PackageReference Include="Sylvan.Data.Csv" Version="1.3.2" />
    <PackageReference Include="Microsoft.Graph" Version="5.62.0" />

    <ProjectReference Include="..\SharedServices\SharedServices.csproj" />
    <ProjectReference Include="..\DataModel\DataModel.csproj" />

  </ItemGroup>

  <!-- Pre-build event to run DataModel generator -->
  <Target Name="RunDataModelGenerator" BeforeTargets="BeforeBuild">
    <Message Text="Running DataModel generator..." Importance="high" />
    <Exec Command="dotnet run --project $(MSBuildProjectDirectory)/../DataModel/DataModel.csproj -- generate" ContinueOnError="false" WorkingDirectory="$(MSBuildProjectDirectory)" />
  </Target>

</Project>
