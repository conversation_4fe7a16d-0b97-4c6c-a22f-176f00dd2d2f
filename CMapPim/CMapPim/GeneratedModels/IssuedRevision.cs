using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.GeneratedModels
{
  public class IssuedRevision : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
  //  private string _sourcePath =  "";
    private static string table = "Issued_Revisions";
    private  static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVApprovedBy",
      "ATVCentralModel",
      "ATVDocumentNumber",
      "ATVDocumentTitle",
      "ATVHideReason",
      "ATVLocalModel",
      "ATVPdfRenditionRelUrl",
      "ATVPlaceholderGuid",
      "ATVReceivedFromContactGuid",
      "ATVRenditionTypes",
      "ATVRevision",
      "ATVTransmittal",
      "ATVUploadedBy",
      "ATVSuitabilityField",
      "ATVCompanyGuid",
      "ATVStatusField",
      "ATVScaleField",
      "ATVDescriptionField",
      "ATVSizeField",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
      "ATVAssocFileCount",
      "ATVIsIssued",
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
      "ATVMultiAssocFile",
      "ATVMultiConfidentialAssocFile",
      "ATVMultiRendition",
      "ATVMultiConfidentialRendition",
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
      "ATVOriginalRevisionLookupId",
      "ATVPlaceholderLookupId",
      "ATVIssueLookupId",
    };
    
    private new static string[] boolAttributeNames = new string[]
    {
      "ATVQAApproval",
    };
    
    private new static string[] dateTimeAttributeNames = new string[]
    {
      "ATVReceivedOn",
      "ATVDateKey",
    };
  
    public string ATVApprovedBy
      {
        get { return GetFieldAsString("ATVApprovedBy");}
        set { SetFieldAsString("ATVApprovedBy",value);}
      }
    
    public string ATVCentralModel
      {
        get { return GetFieldAsString("ATVCentralModel");}
        set { SetFieldAsString("ATVCentralModel",value);}
      }
    
    public string ATVDocumentNumber
      {
        get { return GetFieldAsString("ATVDocumentNumber");}
        set { SetFieldAsString("ATVDocumentNumber",value);}
      }
    
    public string ATVDocumentTitle
      {
        get { return GetFieldAsString("ATVDocumentTitle");}
        set { SetFieldAsString("ATVDocumentTitle",value);}
      }
    
    public string ATVHideReason
      {
        get { return GetFieldAsString("ATVHideReason");}
        set { SetFieldAsString("ATVHideReason",value);}
      }
    
    public string ATVLocalModel
      {
        get { return GetFieldAsString("ATVLocalModel");}
        set { SetFieldAsString("ATVLocalModel",value);}
      }
    
    public string ATVPdfRenditionRelUrl
      {
        get { return GetFieldAsString("ATVPdfRenditionRelUrl");}
        set { SetFieldAsString("ATVPdfRenditionRelUrl",value);}
      }
    
    public string ATVPlaceholderGuid
      {
        get { return GetFieldAsString("ATVPlaceholderGuid");}
        set { SetFieldAsString("ATVPlaceholderGuid",value);}
      }
    
    public string ATVReceivedFromContactGuid
      {
        get { return GetFieldAsString("ATVReceivedFromContactGuid");}
        set { SetFieldAsString("ATVReceivedFromContactGuid",value);}
      }
    
    public string ATVRenditionTypes
      {
        get { return GetFieldAsString("ATVRenditionTypes");}
        set { SetFieldAsString("ATVRenditionTypes",value);}
      }
    
    public string ATVRevision
      {
        get { return GetFieldAsString("ATVRevision");}
        set { SetFieldAsString("ATVRevision",value);}
      }
    
    public string ATVTransmittal
      {
        get { return GetFieldAsString("ATVTransmittal");}
        set { SetFieldAsString("ATVTransmittal",value);}
      }
    
    public string ATVUploadedBy
      {
        get { return GetFieldAsString("ATVUploadedBy");}
        set { SetFieldAsString("ATVUploadedBy",value);}
      }
    
    public string ATVSuitabilityField
      {
        get { return GetFieldAsString("ATVSuitabilityField");}
        set { SetFieldAsString("ATVSuitabilityField",value);}
      }
    
    public string ATVCompanyGuid
      {
        get { return GetFieldAsString("ATVCompanyGuid");}
        set { SetFieldAsString("ATVCompanyGuid",value);}
      }
    
    public string ATVStatusField
      {
        get { return GetFieldAsString("ATVStatusField");}
        set { SetFieldAsString("ATVStatusField",value);}
      }
    
    public string ATVScaleField
      {
        get { return GetFieldAsString("ATVScaleField");}
        set { SetFieldAsString("ATVScaleField",value);}
      }
    
    public string ATVDescriptionField
      {
        get { return GetFieldAsString("ATVDescriptionField");}
        set { SetFieldAsString("ATVDescriptionField",value);}
      }
    
    public string ATVSizeField
      {
        get { return GetFieldAsString("ATVSizeField");}
        set { SetFieldAsString("ATVSizeField",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public int ATVAssocFileCount
      {
        get { return GetFieldAsInteger("ATVAssocFileCount");}
        set { SetFieldAsInteger("ATVAssocFileCount",value);}
      }
    
    public int ATVIsIssued
      {
        get { return GetFieldAsInteger("ATVIsIssued");}
        set { SetFieldAsInteger("ATVIsIssued",value);}
      }
    


      public string ATVMultiAssocFileAsString
      {
        get {
          List<int> ids = GetFieldAsMultiLookup("ATVMultiAssocFile");
          string str = "";
          foreach (int id in ids) {
            str = str + id.ToString() + ",";
          }
          return str;
        }

        set {

          if (value != null && value != "") {

            List<int> ids = new List<int>();
            string[] idstr = value.Split(",");
            foreach (string id in idstr) {
              if (id != "")
              ids.Add(Int32.Parse(id));
            }
            SetFieldAsMultiLookup("ATVMultiAssocFile",ids);
          }
        }

      }

      

      public string ATVMultiConfidentialAssocFileAsString
      {
        get {
          List<int> ids = GetFieldAsMultiLookup("ATVMultiConfidentialAssocFile");
          string str = "";
          foreach (int id in ids) {
            str = str + id.ToString() + ",";
          }
          return str;
        }

        set {

          if (value != null && value != "") {

            List<int> ids = new List<int>();
            string[] idstr = value.Split(",");
            foreach (string id in idstr) {
              if (id != "")
              ids.Add(Int32.Parse(id));
            }
            SetFieldAsMultiLookup("ATVMultiConfidentialAssocFile",ids);
          }
        }

      }

      

      public string ATVMultiRenditionAsString
      {
        get {
          List<int> ids = GetFieldAsMultiLookup("ATVMultiRendition");
          string str = "";
          foreach (int id in ids) {
            str = str + id.ToString() + ",";
          }
          return str;
        }

        set {

          if (value != null && value != "") {

            List<int> ids = new List<int>();
            string[] idstr = value.Split(",");
            foreach (string id in idstr) {
              if (id != "")
              ids.Add(Int32.Parse(id));
            }
            SetFieldAsMultiLookup("ATVMultiRendition",ids);
          }
        }

      }

      

      public string ATVMultiConfidentialRenditionAsString
      {
        get {
          List<int> ids = GetFieldAsMultiLookup("ATVMultiConfidentialRendition");
          string str = "";
          foreach (int id in ids) {
            str = str + id.ToString() + ",";
          }
          return str;
        }

        set {

          if (value != null && value != "") {

            List<int> ids = new List<int>();
            string[] idstr = value.Split(",");
            foreach (string id in idstr) {
              if (id != "")
              ids.Add(Int32.Parse(id));
            }
            SetFieldAsMultiLookup("ATVMultiConfidentialRendition",ids);
          }
        }

      }

          public int ATVOriginalRevision
      {
        get { return GetFieldAsSingleLookup("ATVOriginalRevisionLookupId");}
        set { SetFieldAsSingleLookup("ATVOriginalRevisionLookupId",value);}
      }
    
    public int ATVPlaceholder
      {
        get { return GetFieldAsSingleLookup("ATVPlaceholderLookupId");}
        set { SetFieldAsSingleLookup("ATVPlaceholderLookupId",value);}
      }
    
    public int ATVIssue
      {
        get { return GetFieldAsSingleLookup("ATVIssueLookupId");}
        set { SetFieldAsSingleLookup("ATVIssueLookupId",value);}
      }
    
    public bool ATVQAApproval
      {
        get { return GetFieldAsBool("ATVQAApproval");}
        set { SetFieldAsBool("ATVQAApproval",value);}
      }
    
    public DateTime? ATVReceivedOn
      {
        get { return GetFieldAsDateTime("ATVReceivedOn");}
        set { SetFieldAsDateTime("ATVReceivedOn",value);}
      }
    
    public DateTime? ATVDateKey
      {
        get { return GetFieldAsDateTime("ATVDateKey");}
        set { SetFieldAsDateTime("ATVDateKey",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private IssuedRevision(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public IssuedRevision(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public IssuedRevision()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<IssuedRevision>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,
                
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new IssuedRevision(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else{
        return limRes.ToResult();
      }
    }

    public static async Task<Result<List<IssuedRevision>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<IssuedRevision> mappedItems = new List<IssuedRevision>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new IssuedRevision(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<IssuedRevision>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess && null != limRes.Value)
      {
        return new IssuedRevision(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the IssuedRevision");
        return Result.Fail("There was an error creating the IssuedRevision");
      }
    }

    public async Task<Result<ListItem?>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    

    public async Task<Result> Delete(Context ctx)
    {
          string? id = GetId().ToString();
      if (id == null)
      {
        return Result.Fail("No id set");
      }
      else
        {
        Result limRes = await ctx.GraphApiClient.DeleteListItem(id, table, ctx.SiteId, ctx.GraphClient);
        return limRes;
      }
    }

    /*
    public static Result Write(List<IssuedRevision> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVApprovedBy","ATVCentralModel","ATVDocumentNumber","ATVDocumentTitle","ATVHideReason","ATVLocalModel","ATVPdfRenditionRelUrl","ATVPlaceholderGuid","ATVReceivedFromContactGuid","ATVRenditionTypes","ATVRevision","ATVTransmittal","ATVUploadedBy","ATVSuitabilityField","ATVCompanyGuid","ATVStatusField","ATVScaleField","ATVDescriptionField","ATVSizeField","Title","ATVAssocFileCount","ATVIsIssued","ATVMultiAssocFile","ATVMultiConfidentialAssocFile","ATVMultiRendition","ATVMultiConfidentialRendition","ATVOriginalRevision","ATVPlaceholder","ATVIssue","ATVQAApproval","ATVReceivedOn","ATVDateKey");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<IssuedRevision> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<IssuedRevision>().ToList();
    }
*/
       }
}
