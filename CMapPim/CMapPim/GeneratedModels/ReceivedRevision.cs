using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.GeneratedModels
{
  public class ReceivedRevision : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
  //  private string _sourcePath =  "";
    private static string table = "Received_Revisions";
    private  static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVTransmittalGuid",
      "ATVInternetMessageId",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
      "ATVMultiRendition",
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
      "ATVRevisionLookupLookupId",
    };
    
    private new static string[] boolAttributeNames = new string[]
    {
    };
    
    private new static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string ATVTransmittalGuid
      {
        get { return GetFieldAsString("ATVTransmittalGuid");}
        set { SetFieldAsString("ATVTransmittalGuid",value);}
      }
    
    public string ATVInternetMessageId
      {
        get { return GetFieldAsString("ATVInternetMessageId");}
        set { SetFieldAsString("ATVInternetMessageId",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    


      public string ATVMultiRenditionAsString
      {
        get {
          List<int> ids = GetFieldAsMultiLookup("ATVMultiRendition");
          string str = "";
          foreach (int id in ids) {
            str = str + id.ToString() + ",";
          }
          return str;
        }

        set {

          if (value != null && value != "") {

            List<int> ids = new List<int>();
            string[] idstr = value.Split(",");
            foreach (string id in idstr) {
              if (id != "")
              ids.Add(Int32.Parse(id));
            }
            SetFieldAsMultiLookup("ATVMultiRendition",ids);
          }
        }

      }

          public int ATVRevisionLookup
      {
        get { return GetFieldAsSingleLookup("ATVRevisionLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVRevisionLookupLookupId",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private ReceivedRevision(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public ReceivedRevision(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public ReceivedRevision()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<ReceivedRevision>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,
                
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new ReceivedRevision(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else{
        return limRes.ToResult();
      }
    }

    public static async Task<Result<List<ReceivedRevision>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<ReceivedRevision> mappedItems = new List<ReceivedRevision>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new ReceivedRevision(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<ReceivedRevision>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess && null != limRes.Value)
      {
        return new ReceivedRevision(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the ReceivedRevision");
        return Result.Fail("There was an error creating the ReceivedRevision");
      }
    }

    public async Task<Result<ListItem?>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    

    public async Task<Result> Delete(Context ctx)
    {
          string? id = GetId().ToString();
      if (id == null)
      {
        return Result.Fail("No id set");
      }
      else
        {
        Result limRes = await ctx.GraphApiClient.DeleteListItem(id, table, ctx.SiteId, ctx.GraphClient);
        return limRes;
      }
    }

    /*
    public static Result Write(List<ReceivedRevision> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVTransmittalGuid","ATVInternetMessageId","Title","ATVMultiRendition","ATVRevisionLookup");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<ReceivedRevision> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<ReceivedRevision>().ToList();
    }
*/
       }
}
