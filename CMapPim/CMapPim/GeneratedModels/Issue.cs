using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.GeneratedModels
{
  public class Issue : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
  //  private string _sourcePath =  "";
    private static string table = "Issues";
    private  static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVIssueDescription",
      "ATVIssuedBy",
      "ATVIssueNotes",
      "ATVIssueComments",
      "ATVIssueMedia",
      "ATVReason",
      "ATVTransmitStatus",
      "ATVTransmitError",
      "ATVFailedIssue",
      "ATVIssueContactsGuids",
      "ATVCreatedBy",
      "ATVVoidedReason",
      "ATVIssueDocumentsJson",
      "ATVIssueContactsJson",
      "ATVTransmittalReference",
      "ATVSharingLink",
      "ATVTransmittalDocTemplate",
      "ATVRecordZip",
      "ATVTransmittalDoc",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
      "ATVIssueNumber",
      "ATVNumberOfDocs",
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
    };
    
    private new static string[] boolAttributeNames = new string[]
    {
      "ATVConfidential",
      "ATVHasOutgoingNamingScheme",
      "ATVDeleteOnExpiry",
      "ATVInformal",
      "ATVRetrospective",
    };
    
    private new static string[] dateTimeAttributeNames = new string[]
    {
      "ATVIssuedOn",
      "ATVVoided",
      "ATVTokenExpiry",
    };
  
    public string ATVIssueDescription
      {
        get { return GetFieldAsString("ATVIssueDescription");}
        set { SetFieldAsString("ATVIssueDescription",value);}
      }
    
    public string ATVIssuedBy
      {
        get { return GetFieldAsString("ATVIssuedBy");}
        set { SetFieldAsString("ATVIssuedBy",value);}
      }
    
    public string ATVIssueNotes
      {
        get { return GetFieldAsString("ATVIssueNotes");}
        set { SetFieldAsString("ATVIssueNotes",value);}
      }
    
    public string ATVIssueComments
      {
        get { return GetFieldAsString("ATVIssueComments");}
        set { SetFieldAsString("ATVIssueComments",value);}
      }
    
    public string ATVIssueMedia
      {
        get { return GetFieldAsString("ATVIssueMedia");}
        set { SetFieldAsString("ATVIssueMedia",value);}
      }
    
    public string ATVReason
      {
        get { return GetFieldAsString("ATVReason");}
        set { SetFieldAsString("ATVReason",value);}
      }
    
    public string ATVTransmitStatus
      {
        get { return GetFieldAsString("ATVTransmitStatus");}
        set { SetFieldAsString("ATVTransmitStatus",value);}
      }
    
    public string ATVTransmitError
      {
        get { return GetFieldAsString("ATVTransmitError");}
        set { SetFieldAsString("ATVTransmitError",value);}
      }
    
    public string ATVFailedIssue
      {
        get { return GetFieldAsString("ATVFailedIssue");}
        set { SetFieldAsString("ATVFailedIssue",value);}
      }
    
    public string ATVIssueContactsGuids
      {
        get { return GetFieldAsString("ATVIssueContactsGuids");}
        set { SetFieldAsString("ATVIssueContactsGuids",value);}
      }
    
    public string ATVCreatedBy
      {
        get { return GetFieldAsString("ATVCreatedBy");}
        set { SetFieldAsString("ATVCreatedBy",value);}
      }
    
    public string ATVVoidedReason
      {
        get { return GetFieldAsString("ATVVoidedReason");}
        set { SetFieldAsString("ATVVoidedReason",value);}
      }
    
    public string ATVIssueDocumentsJson
      {
        get { return GetFieldAsString("ATVIssueDocumentsJson");}
        set { SetFieldAsString("ATVIssueDocumentsJson",value);}
      }
    
    public string ATVIssueContactsJson
      {
        get { return GetFieldAsString("ATVIssueContactsJson");}
        set { SetFieldAsString("ATVIssueContactsJson",value);}
      }
    
    public string ATVTransmittalReference
      {
        get { return GetFieldAsString("ATVTransmittalReference");}
        set { SetFieldAsString("ATVTransmittalReference",value);}
      }
    
    public string ATVSharingLink
      {
        get { return GetFieldAsString("ATVSharingLink");}
        set { SetFieldAsString("ATVSharingLink",value);}
      }
    
    public string ATVTransmittalDocTemplate
      {
        get { return GetFieldAsString("ATVTransmittalDocTemplate");}
        set { SetFieldAsString("ATVTransmittalDocTemplate",value);}
      }
    
    public string ATVRecordZip
      {
        get { return GetFieldAsString("ATVRecordZip");}
        set { SetFieldAsString("ATVRecordZip",value);}
      }
    
    public string ATVTransmittalDoc
      {
        get { return GetFieldAsString("ATVTransmittalDoc");}
        set { SetFieldAsString("ATVTransmittalDoc",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public int ATVIssueNumber
      {
        get { return GetFieldAsInteger("ATVIssueNumber");}
        set { SetFieldAsInteger("ATVIssueNumber",value);}
      }
    
    public int ATVNumberOfDocs
      {
        get { return GetFieldAsInteger("ATVNumberOfDocs");}
        set { SetFieldAsInteger("ATVNumberOfDocs",value);}
      }
    
    public bool ATVConfidential
      {
        get { return GetFieldAsBool("ATVConfidential");}
        set { SetFieldAsBool("ATVConfidential",value);}
      }
    
    public bool ATVHasOutgoingNamingScheme
      {
        get { return GetFieldAsBool("ATVHasOutgoingNamingScheme");}
        set { SetFieldAsBool("ATVHasOutgoingNamingScheme",value);}
      }
    
    public bool ATVDeleteOnExpiry
      {
        get { return GetFieldAsBool("ATVDeleteOnExpiry");}
        set { SetFieldAsBool("ATVDeleteOnExpiry",value);}
      }
    
    public bool ATVInformal
      {
        get { return GetFieldAsBool("ATVInformal");}
        set { SetFieldAsBool("ATVInformal",value);}
      }
    
    public bool ATVRetrospective
      {
        get { return GetFieldAsBool("ATVRetrospective");}
        set { SetFieldAsBool("ATVRetrospective",value);}
      }
    
    public DateTime? ATVIssuedOn
      {
        get { return GetFieldAsDateTime("ATVIssuedOn");}
        set { SetFieldAsDateTime("ATVIssuedOn",value);}
      }
    
    public DateTime? ATVVoided
      {
        get { return GetFieldAsDateTime("ATVVoided");}
        set { SetFieldAsDateTime("ATVVoided",value);}
      }
    
    public DateTime? ATVTokenExpiry
      {
        get { return GetFieldAsDateTime("ATVTokenExpiry");}
        set { SetFieldAsDateTime("ATVTokenExpiry",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private Issue(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public Issue(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public Issue()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<Issue>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,
                
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new Issue(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else{
        return limRes.ToResult();
      }
    }

    public static async Task<Result<List<Issue>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<Issue> mappedItems = new List<Issue>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new Issue(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<Issue>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess && null != limRes.Value)
      {
        return new Issue(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the Issue");
        return Result.Fail("There was an error creating the Issue");
      }
    }

    public async Task<Result<ListItem?>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    

    public async Task<Result> Delete(Context ctx)
    {
          string? id = GetId().ToString();
      if (id == null)
      {
        return Result.Fail("No id set");
      }
      else
        {
        Result limRes = await ctx.GraphApiClient.DeleteListItem(id, table, ctx.SiteId, ctx.GraphClient);
        return limRes;
      }
    }

    /*
    public static Result Write(List<Issue> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVIssueDescription","ATVIssuedBy","ATVIssueNotes","ATVIssueComments","ATVIssueMedia","ATVReason","ATVTransmitStatus","ATVTransmitError","ATVFailedIssue","ATVIssueContactsGuids","ATVCreatedBy","ATVVoidedReason","ATVIssueDocumentsJson","ATVIssueContactsJson","ATVTransmittalReference","ATVSharingLink","ATVTransmittalDocTemplate","ATVRecordZip","ATVTransmittalDoc","Title","ATVIssueNumber","ATVNumberOfDocs","ATVConfidential","ATVHasOutgoingNamingScheme","ATVDeleteOnExpiry","ATVInformal","ATVRetrospective","ATVIssuedOn","ATVVoided","ATVTokenExpiry");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<Issue> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<Issue>().ToList();
    }
*/
       }
}
