using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.GeneratedModels
{
  public class Placeholder : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
  //  private string _sourcePath =  "";
    private static string table = "DLM_Placeholders";
    private  static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVApprovedBy",
      "ATVDMSPublishStatus",
      "ATVControlLifecycle",
      "ATVDocumentNumber",
      "ATVDocumentTitle",
      "ATVHideReason",
      "ATVObsoleted",
      "ATVLastRevisionStatusDisplay",
      "ATVMainExt",
      "ATVNumber",
      "ATVSearchRefiner",
      "ATVCompanyGuid",
      "ATVScheme",
      "ATVDateISO8601",
      "ATVArchiveStatus",
      "ATVProject<PERSON>ield",
      "ATVCompanyField",
      "ATVNumberField",
      "ATVZoneField",
      "ATVLevelField",
      "ATVRoleField",
      "ATVTypeField",
      "ATVCategoryField",
      "ATVGennumberField",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
      "ATVDocumentLookupLookupId",
      "ATVConfidentialDocumentLookupLookupId",
      "ATVLastRevisionLookupId",
      "ATVSupersededByLookupId",
    };
    
    private new static string[] boolAttributeNames = new string[]
    {
      "ATVIsSubProjectStub",
      "ATVQAApproval",
      "ATVReceived",
      "ATVConfidential",
      "ATVUseDocTemplate",
      "ATVRequireAllReviewers",
    };
    
    private new static string[] dateTimeAttributeNames = new string[]
    {
      "ATVReceivedOn",
      "ATVDateKey",
    };
  
    public string ATVApprovedBy
      {
        get { return GetFieldAsString("ATVApprovedBy");}
        set { SetFieldAsString("ATVApprovedBy",value);}
      }
    
    public string ATVDMSPublishStatus
      {
        get { return GetFieldAsString("ATVDMSPublishStatus");}
        set { SetFieldAsString("ATVDMSPublishStatus",value);}
      }
    
    public string ATVControlLifecycle
      {
        get { return GetFieldAsString("ATVControlLifecycle");}
        set { SetFieldAsString("ATVControlLifecycle",value);}
      }
    
    public string ATVDocumentNumber
      {
        get { return GetFieldAsString("ATVDocumentNumber");}
        set { SetFieldAsString("ATVDocumentNumber",value);}
      }
    
    public string ATVDocumentTitle
      {
        get { return GetFieldAsString("ATVDocumentTitle");}
        set { SetFieldAsString("ATVDocumentTitle",value);}
      }
    
    public string ATVHideReason
      {
        get { return GetFieldAsString("ATVHideReason");}
        set { SetFieldAsString("ATVHideReason",value);}
      }
    
    public string ATVObsoleted
      {
        get { return GetFieldAsString("ATVObsoleted");}
        set { SetFieldAsString("ATVObsoleted",value);}
      }
    
    public string ATVLastRevisionStatusDisplay
      {
        get { return GetFieldAsString("ATVLastRevisionStatusDisplay");}
        set { SetFieldAsString("ATVLastRevisionStatusDisplay",value);}
      }
    
    public string ATVMainExt
      {
        get { return GetFieldAsString("ATVMainExt");}
        set { SetFieldAsString("ATVMainExt",value);}
      }
    
    public string ATVNumber
      {
        get { return GetFieldAsString("ATVNumber");}
        set { SetFieldAsString("ATVNumber",value);}
      }
    
    public string ATVSearchRefiner
      {
        get { return GetFieldAsString("ATVSearchRefiner");}
        set { SetFieldAsString("ATVSearchRefiner",value);}
      }
    
    public string ATVCompanyGuid
      {
        get { return GetFieldAsString("ATVCompanyGuid");}
        set { SetFieldAsString("ATVCompanyGuid",value);}
      }
    
    public string ATVScheme
      {
        get { return GetFieldAsString("ATVScheme");}
        set { SetFieldAsString("ATVScheme",value);}
      }
    
    public string ATVDateISO8601
      {
        get { return GetFieldAsString("ATVDateISO8601");}
        set { SetFieldAsString("ATVDateISO8601",value);}
      }
    
    public string ATVArchiveStatus
      {
        get { return GetFieldAsString("ATVArchiveStatus");}
        set { SetFieldAsString("ATVArchiveStatus",value);}
      }
    
    public string ATVProjectField
      {
        get { return GetFieldAsString("ATVProjectField");}
        set { SetFieldAsString("ATVProjectField",value);}
      }
    
    public string ATVCompanyField
      {
        get { return GetFieldAsString("ATVCompanyField");}
        set { SetFieldAsString("ATVCompanyField",value);}
      }
    
    public string ATVNumberField
      {
        get { return GetFieldAsString("ATVNumberField");}
        set { SetFieldAsString("ATVNumberField",value);}
      }
    
    public string ATVZoneField
      {
        get { return GetFieldAsString("ATVZoneField");}
        set { SetFieldAsString("ATVZoneField",value);}
      }
    
    public string ATVLevelField
      {
        get { return GetFieldAsString("ATVLevelField");}
        set { SetFieldAsString("ATVLevelField",value);}
      }
    
    public string ATVRoleField
      {
        get { return GetFieldAsString("ATVRoleField");}
        set { SetFieldAsString("ATVRoleField",value);}
      }
    
    public string ATVTypeField
      {
        get { return GetFieldAsString("ATVTypeField");}
        set { SetFieldAsString("ATVTypeField",value);}
      }
    
    public string ATVCategoryField
      {
        get { return GetFieldAsString("ATVCategoryField");}
        set { SetFieldAsString("ATVCategoryField",value);}
      }
    
    public string ATVGennumberField
      {
        get { return GetFieldAsString("ATVGennumberField");}
        set { SetFieldAsString("ATVGennumberField",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public int ATVDocumentLookup
      {
        get { return GetFieldAsSingleLookup("ATVDocumentLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVDocumentLookupLookupId",value);}
      }
    
    public int ATVConfidentialDocumentLookup
      {
        get { return GetFieldAsSingleLookup("ATVConfidentialDocumentLookupLookupId");}
        set { SetFieldAsSingleLookup("ATVConfidentialDocumentLookupLookupId",value);}
      }
    
    public int ATVLastRevision
      {
        get { return GetFieldAsSingleLookup("ATVLastRevisionLookupId");}
        set { SetFieldAsSingleLookup("ATVLastRevisionLookupId",value);}
      }
    
    public int ATVSupersededBy
      {
        get { return GetFieldAsSingleLookup("ATVSupersededByLookupId");}
        set { SetFieldAsSingleLookup("ATVSupersededByLookupId",value);}
      }
    
    public bool ATVIsSubProjectStub
      {
        get { return GetFieldAsBool("ATVIsSubProjectStub");}
        set { SetFieldAsBool("ATVIsSubProjectStub",value);}
      }
    
    public bool ATVQAApproval
      {
        get { return GetFieldAsBool("ATVQAApproval");}
        set { SetFieldAsBool("ATVQAApproval",value);}
      }
    
    public bool ATVReceived
      {
        get { return GetFieldAsBool("ATVReceived");}
        set { SetFieldAsBool("ATVReceived",value);}
      }
    
    public bool ATVConfidential
      {
        get { return GetFieldAsBool("ATVConfidential");}
        set { SetFieldAsBool("ATVConfidential",value);}
      }
    
    public bool ATVUseDocTemplate
      {
        get { return GetFieldAsBool("ATVUseDocTemplate");}
        set { SetFieldAsBool("ATVUseDocTemplate",value);}
      }
    
    public bool ATVRequireAllReviewers
      {
        get { return GetFieldAsBool("ATVRequireAllReviewers");}
        set { SetFieldAsBool("ATVRequireAllReviewers",value);}
      }
    
    public DateTime? ATVReceivedOn
      {
        get { return GetFieldAsDateTime("ATVReceivedOn");}
        set { SetFieldAsDateTime("ATVReceivedOn",value);}
      }
    
    public DateTime? ATVDateKey
      {
        get { return GetFieldAsDateTime("ATVDateKey");}
        set { SetFieldAsDateTime("ATVDateKey",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private Placeholder(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public Placeholder(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public Placeholder()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<Placeholder>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,
                
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new Placeholder(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else{
        return limRes.ToResult();
      }
    }

    public static async Task<Result<List<Placeholder>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<Placeholder> mappedItems = new List<Placeholder>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new Placeholder(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<Placeholder>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess && null != limRes.Value)
      {
        return new Placeholder(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the Placeholder");
        return Result.Fail("There was an error creating the Placeholder");
      }
    }

    public async Task<Result<ListItem?>> Update(Context ctx)
    {     
    ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    

    public async Task<Result> Delete(Context ctx)
    {
          string? id = GetId().ToString();
      if (id == null)
      {
        return Result.Fail("No id set");
      }
      else
        {
        Result limRes = await ctx.GraphApiClient.DeleteListItem(id, table, ctx.SiteId, ctx.GraphClient);
        return limRes;
      }
    }

    /*
    public static Result Write(List<Placeholder> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVApprovedBy","ATVDMSPublishStatus","ATVControlLifecycle","ATVDocumentNumber","ATVDocumentTitle","ATVHideReason","ATVObsoleted","ATVLastRevisionStatusDisplay","ATVMainExt","ATVNumber","ATVSearchRefiner","ATVCompanyGuid","ATVScheme","ATVDateISO8601","ATVArchiveStatus","ATVProjectField","ATVCompanyField","ATVNumberField","ATVZoneField","ATVLevelField","ATVRoleField","ATVTypeField","ATVCategoryField","ATVGennumberField","Title","ATVDocumentLookup","ATVConfidentialDocumentLookup","ATVLastRevision","ATVSupersededBy","ATVIsSubProjectStub","ATVQAApproval","ATVReceived","ATVConfidential","ATVUseDocTemplate","ATVRequireAllReviewers","ATVReceivedOn","ATVDateKey");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<Placeholder> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<Placeholder>().ToList();
    }
*/
       }
}
